"""
Batch email generation functionality for OpenEngage using OpenAI's Batch API.
"""
import os
import json
import yaml
import time
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
import pandas as pd
from openai import OpenAI
from dotenv import load_dotenv

# Set validate_email to None initially
validate_email = None

# Try to import guardrails validation
try:
    # Try to import from the openengage package
    from openengage.guardrails.guardrails_email import validate_email
except ImportError:
    # Add guardrails directory to path
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        guardrails_dir = os.path.join(parent_dir, 'guardrails')
        sys.path.insert(0, guardrails_dir)

        # Try direct import - use a more specific import to avoid IDE warnings
        try:
            # This is a dynamic import that will be resolved at runtime
            import importlib.util
            spec = importlib.util.spec_from_file_location("guardrails_email",
                                                         os.path.join(guardrails_dir, "guardrails_email.py"))
            if spec and spec.loader:
                guardrails_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(guardrails_module)
                validate_email = guardrails_module.validate_email
        except ImportError:
            pass
    except Exception:
        pass

# Load environment variables
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def generate_emails_batch(
    data_df: pd.DataFrame,
    products: List[Dict[str, Any]],  # This will already be filtered by organization URL if needed
    progress_callback: Optional[Callable] = None,
    batch_size: int = 6000  # Default batch size of 20 for faster processing
) -> pd.DataFrame:
    """
    Generate emails in batch using OpenAI's Batch API.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        products (List[Dict]): List of product data dictionaries
        progress_callback (callable, optional): Function to report progress

    Returns:
        pd.DataFrame: DataFrame with generated email content
    """
    # Create a mapping of user rows to matched products
    user_product_mapping = {}

    # Create lists for batch tasks and debug info
    tasks = []
    users_with_behavior = []
    users_without_behavior = []

    # System prompt for email generation - using the same prompt as in crew_manager.py
    system_prompt = """
    You are an expert email marketing specialist. Your task is to create personalized marketing emails
    based on user behavior and product information.

    Generate an email campaign for the specified stage using the provided details.

    Your response should be in JSON format with the following structure:
    {
        "subject": "Your compelling subject line here",
        "preheader": "Your brief pre-header text that complements the subject line",
        "content": "Your email body content here"
    }

    Keep the email concise, engaging, and focused on the product's benefits that match the user's interests.
    Make sure to include relevant product features and benefits.
    Add UTM-tagged links where appropriate.
    You can pitch a collateral if it is relevant to the email.
    """

    # Report progress
    if progress_callback:
        progress_callback(0, len(data_df), "Processing users and creating tasks...")

    # Process all users in a single loop - match products and create tasks
    for idx, row in data_df.iterrows():
        # Check if user has behavior data
        has_behavior_data = ("Behaviour data not found" not in row['user_behaviour']) or ("last opened an email" in row['user_behaviour'])

        # Find the matched product
        if has_behavior_data:
            try:
                from core.product_selector import select_product_for_user
                matched_product, similarity = select_product_for_user(row, products)
                users_with_behavior.append(idx)
            except Exception as e:
                print(f"Error finding similar product for user {idx}: {str(e)}")
                matched_product, similarity = select_product_for_user(row, products)
                has_behavior_data = False  # Treat as no behavior data due to error
                users_without_behavior.append(idx)
        else:
            import random
            matched_product = random.choice(products)
            similarity = 0
            users_without_behavior.append(idx)

        # Store the mapping
        user_product_mapping[idx] = {
            'product': matched_product,
            'similarity': similarity,
            'has_behavior': has_behavior_data
        }
        print(users_without_behavior)

        # Process based on behavior data
        if not has_behavior_data:
            # Try to find a matching template for users without behavior data
            template_file = f'data/templates/{row["user_stage"].lower().replace(" ", "_")}.json'
            template_found = False

            if os.path.exists(template_file):
                try:
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        # Filter templates for this product (only verified ones)
                        matching_templates = [t for t in templates
                                            if t.get('product_data', {}).get('Product_Name') == matched_product.get('Product_Name') and t.get('verified', False) is True]

                        if matching_templates:
                            # Use the most recent template
                            template = matching_templates[-1]

                            if template:
                                # Update DataFrame with template data
                                # Get template content
                                subject = template.get('template', {}).get('subject', '')
                                body = template.get('template', {}).get('body', '')
                                preheader = template.get('template', {}).get('preheader', '')

                                # Update DataFrame with template data
                                data_df.at[idx, 'Subject'] = subject
                                data_df.at[idx, 'Mail_Content'] = body
                                data_df.at[idx, 'Preheader'] = preheader
                                data_df.at[idx, 'Matched_Product'] = matched_product.get('Product_Name', '')
                                data_df.at[idx, 'Similarity_Score'] = round(float(similarity) * 100, 2)
                                data_df.at[idx, 'Validation_Message'] = "Used template instead of generation"
                                data_df.at[idx, 'Template_Name'] = template.get('template', {}).get('template_name', '')

                                # Generate HTML content for the template
                                try:
                                    # Import text_to_html if not already imported
                                    if 'text_to_html' not in locals():
                                        from core.email_formatter import text_to_html

                                    # Create email content dictionary
                                    email_content_dict = {
                                        'subject': subject,
                                        'content': body,
                                        'preheader': preheader
                                    }

                                    # Create settings for HTML conversion
                                    settings = {
                                        "sender_name": matched_product.get('Company_Name', 'OpenEngage Team'),
                                        "style": "friendly",
                                        "length": "100-150 words",
                                        "utm_source": "mass_email",
                                        "utm_medium": "email",
                                        "utm_campaign": "personalized_campaign",
                                        "utm_content": row['user_stage'].lower().replace(" ", "_"),
                                        "template_context": {
                                            "base_template": {
                                                "product_data": matched_product
                                            }
                                        }
                                    }

                                    # Add organization URL to settings
                                    org_url = matched_product.get('Company_URL') or matched_product.get('organization_url')
                                    if org_url:
                                        settings['organization_url'] = org_url

                                    # Convert to HTML
                                    html_content = text_to_html(
                                        email_content_dict,
                                        product_url=matched_product.get('Product_URL'),
                                        product_name=matched_product.get('Product_Name'),
                                        communication_settings=settings,
                                        recipient_email=row.get('user_email'),
                                        recipient_first_name=row.get('first_name')
                                    )

                                    # Add HTML content to the DataFrame
                                    data_df.at[idx, 'HTML_Content'] = html_content
                                except Exception as e:
                                    print(f"Error generating HTML for template user {idx}: {str(e)}")
                                    data_df.at[idx, 'HTML_Content'] = f"<p>Error generating HTML: {str(e)}</p>"

                                template_found = True
                except Exception as e:
                    print(f"Error loading template for user {idx}: {str(e)}")

            if not template_found:
                # If no template found, add to batch processing
                users_with_behavior.append(idx)
                user_product_mapping[idx]['has_behavior'] = True  # Treat as having behavior for batch processing

        # Create batch task for users with behavior data or without templates
        if has_behavior_data or (not has_behavior_data and not template_found):
            # Get current stage details
            current_stage = {
                "current_stage": row['user_stage'],
                "goal_stage": "Converted",  # Default goal
                "description": f"Move user from {row['user_stage']} to Converted"
            }

            # Try to find a template first to use as a reference
            template_file = f'data/templates/{row["user_stage"].lower().replace(" ", "_")}.json'
            template_reference = None
            base_template = None

            if os.path.exists(template_file):
                try:
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        matching_templates = [t for t in templates
                                            if t.get('product_data', {}).get('Product_Name') == matched_product.get('Product_Name') and t.get('verified', False) is True]
                        if matching_templates:
                            template_reference = matching_templates[-1]
                            # Create base template for consistency with mass_email_generator.py
                            base_template = {
                                "template": template_reference.get('template', {}),
                                "product_data": template_reference.get('product_data', matched_product),
                                "communication_settings": template_reference.get('settings', {
                                    "tone": "professional",
                                    "style": "formal",
                                    "length": "100-150 words",
                                    "sender_name": "OpenEngage Team",
                                    "brand_personality": "Professional, Helpful, Trustworthy",
                                    "tone_of_voice": "Professional, Informative"
                                })
                            }
                except Exception as e:
                    print(f"Error reading template file: {str(e)}")

            # Load prompt template from prompts.yml
            prompts_path = Path(__file__).parent.parent.parent.parent / 'config' / 'prompts.yml'
            campaign_template = ""
            try:
                with open(prompts_path, 'r') as f:
                    prompts = yaml.safe_load(f)
                    campaign_template = prompts.get('campaign_generation', {}).get('template', '')
            except Exception as e:
                print(f"Error loading prompts.yml: {str(e)}")

            # If template couldn't be loaded, use a default template
            if not campaign_template:
                user_message = f"""
                Generate an email campaign for the {row['user_stage']} stage using these details:

                Product Details:
                Target Product: {matched_product.get('Product_Name', '')}
                Target Product URL: {matched_product.get('Product_URL', '')}
                Target Product Summary: {matched_product.get('Product_Description', '')}
                Target Product Features:
                {chr(10).join('- ' + feature for feature in matched_product.get('Product_Features', []))}
                Target Product Collaterals: {str(matched_product.get('Collaterals', []))}

                Stage Details:
                Current Stage: {current_stage['current_stage']}
                Goal Stage: {current_stage['goal_stage']}
                Stage Description: {current_stage['description']}

                Communication Settings:
                Tone: professional
                Style: friendly
                Length: 100-150 words
                Sender Name: {matched_product.get('Company_Name', 'OpenEngage Team')}
                First Name: {row['first_name']}
                User Behavior: {row['user_behaviour'] if has_behavior_data else f"Interested in {matched_product.get('Product_Name')}"}

                Instructions:
                1. Write an engaging email that moves the user from {current_stage['current_stage']} to {current_stage['goal_stage']}
                2. Use the specified tone and style
                3. Include relevant product features and benefits
                4. Add UTM-tagged links where appropriate
                5. You can pitch a collateral if it is relevant to the email

                Email Content:
                Subject: [Write your subject line here]
                Pre-header: [Write a brief pre-header text that complements the subject line]
                [Write your email body here]
                """
            else:
                # Format the template with product and user data
                subject = ""
                body = ""
                if template_reference:
                    subject = template_reference.get('template', {}).get('subject', '')
                    body = template_reference.get('template', {}).get('body', '')

                # Extract communication settings
                tone = "professional"
                style = "friendly"
                length = "100-150 words"
                sender_name = matched_product.get('Company_Name', 'OpenEngage Team')
                brand_personality = "Professional, Helpful, Trustworthy"
                brand_tone_of_voice = "Professional, Informative"

                if base_template:
                    comm_settings = base_template.get('communication_settings', {})
                    tone = comm_settings.get('tone', tone)
                    style = comm_settings.get('style', style)
                    length = comm_settings.get('length', length)
                    sender_name = comm_settings.get('sender_name', sender_name)
                    brand_personality = comm_settings.get('brand_personality', brand_personality)
                    brand_tone_of_voice = comm_settings.get('tone_of_voice', brand_tone_of_voice)

                # Format product details as a string
                product_details = f"""Target Product: {matched_product.get('Product_Name', '')}
                Target Product URL: {matched_product.get('Product_URL', '')}
                Target Product Summary: {matched_product.get('Product_Description', '')}
                Target Product Features:
                {chr(10).join('- ' + feature for feature in matched_product.get('Product_Features', []))}
                Target Product Collaterals: {str(matched_product.get('Collaterals', []))}
                """

                # Format instructions
                instructions = f"""1. Write an engaging email that moves the user from {current_stage['current_stage']} to {current_stage['goal_stage']}
                2. Use the specified tone and style
                3. Include relevant product features and benefits
                4. Add UTM-tagged links where appropriate
                5. You can pitch a collateral if it is relevant to the email
                6. You must follow this exact format:
                Email Content:
                Subject: [Write your subject line here]
                Pre-header: [Write a brief pre-header text that complements the subject line]
                [Write your email body here]
                """

                # Format the template with all required variables
                user_message = campaign_template.format(
                    stage=row['user_stage'],
                    subject=subject,
                    body=body,
                    product_details=product_details,
                    tone=tone,
                    style=style,
                    length=length,
                    sender_name=sender_name,
                    brand_personality=brand_personality,
                    brand_tone_of_voice=brand_tone_of_voice,
                    first_name=row['first_name'],
                    user_behavior=row['user_behaviour'] if has_behavior_data else f"Interested in {matched_product.get('Product_Name')}",
                    instructions=instructions
                )

            # Store template information for later use
            template_name = None
            if template_reference:
                template_name = template_reference.get('template', {}).get('template_name', '')
                
            # Create the task
            task = {
                "custom_id": f"task-{idx}",
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": {
                    "model": "ft:gpt-4o-mini-2024-07-18:analytics-vidhya:oev1mailgen:B7xdiKXj",
                    "temperature": 0.7,
                    "response_format": {"type": "json_object"},
                    "messages": [
                        {
                            "role": "system",
                            "content": system_prompt
                        },
                        {
                            "role": "user",
                            "content": user_message
                        }
                    ]
                },
                "metadata": {
                    "template_name": template_name
                }
            }

            tasks.append(task)

        # Update progress
        if progress_callback and idx % 10 == 0:
            progress_callback(idx, len(data_df), f"Processed {idx}/{len(data_df)} users...")

    # Report progress
    if progress_callback:
        progress_callback(len(data_df), len(data_df),
                         f"Completed processing all users. Created {len(tasks)} batch tasks.")

    # Skip if no tasks to process
    if not tasks:
        print("No tasks to process, skipping batch processing")
        if progress_callback:
            progress_callback(0, len(data_df), "No users to process in batches.")
        return data_df

    # Split tasks into smaller batches for faster processing
    batched_tasks = [tasks[i:i + batch_size] for i in range(0, len(tasks), batch_size)]

    # Report progress
    if progress_callback:
        progress_callback(0, len(data_df), f"Processing {len(tasks)} batches in {len(batched_tasks)} batches of size {batch_size}...")

    # Process each batch
    batch_results = []

    for batch_index, batch in enumerate(batched_tasks):
        # Create batch file
        batch_file_path = f"data/batch_tasks_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{batch_index}.jsonl"
        os.makedirs("data", exist_ok=True)

        # Report progress
        if progress_callback:
            progress_callback(batch_index, len(batched_tasks), f"Creating batch file {batch_index+1}/{len(batched_tasks)}...")

        # Write batch file
        with open(batch_file_path, 'w') as file:
            for task in batch:
                file.write(json.dumps(task) + '\n')

        # Report progress
        if progress_callback:
            progress_callback(batch_index, len(batched_tasks), f"Uploading batch file {batch_index+1}/{len(batched_tasks)}...")

        # Upload batch file
        batch_file = client.files.create(
            file=open(batch_file_path, "rb"),
            purpose="batch"
        )

        # Report progress
        if progress_callback:
            progress_callback(batch_index, len(batched_tasks), f"Creating batch job {batch_index+1}/{len(batched_tasks)}...")

        # Create batch job
        batch_job = client.batches.create(
            input_file_id=batch_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h"
        )

        # Store batch job info
        batch_results.append({
            "batch_index": batch_index,
            "batch_job": batch_job,
            "batch_file_path": batch_file_path,
            "tasks": batch
        })

    # Process batch results
    completed_batches = 0
    all_results = []

    # Report progress
    if progress_callback:
        progress_callback(0, len(batched_tasks), "Waiting for batch jobs to complete...")

    # Wait for batch jobs to complete
    # Note: We'll wait indefinitely until all batches are completed
    while completed_batches < len(batch_results):
        # Update progress message to show we're still waiting
        if progress_callback:
            progress_callback(completed_batches, len(batched_tasks),
                             f"Waiting for all batch jobs to complete... ({completed_batches}/{len(batch_results)} done)")

        # Check each batch job status
        for batch_info in batch_results:
            # Skip already completed batches
            if "completed" in batch_info:
                continue

            # Check batch job status
            batch_job = client.batches.retrieve(batch_info["batch_job"].id)
            batch_info["status"] = batch_job.status

            # Update progress
            if progress_callback:
                progress_callback(completed_batches, len(batched_tasks),
                                 f"Batch {batch_info['batch_index']+1}/{len(batched_tasks)} status: {batch_job.status}")

            # If completed, process results
            if batch_job.status == "completed":
                # Mark as completed
                batch_info["completed"] = True
                completed_batches += 1

                # Report progress
                if progress_callback:
                    progress_callback(completed_batches, len(batched_tasks),
                                     f"Retrieving results for batch {batch_info['batch_index']+1}/{len(batched_tasks)}...")

                # Get result file
                result_file_id = batch_job.output_file_id
                result_content = client.files.content(result_file_id).content

                # Save result file
                result_file_path = f"data/batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{batch_info['batch_index']}.jsonl"
                with open(result_file_path, 'wb') as file:
                    file.write(result_content)

                # Parse results
                batch_results_list = []
                with open(result_file_path, 'r') as file:
                    for line in file:
                        batch_results_list.append(json.loads(line.strip()))

                # Store results
                batch_info["results"] = batch_results_list
                all_results.extend(batch_results_list)

        # If all batches are completed, break
        if completed_batches == len(batch_results):
            break

        # Wait before checking again (5 minutes)
        wait_minutes = 5
        if progress_callback:
            progress_callback(completed_batches, len(batched_tasks),
                             f"Waiting for {wait_minutes} minutes before checking batch status again...")
        time.sleep(wait_minutes * 60)  # Convert minutes to seconds

    # Import required functions for HTML conversion
    from core.email_formatter import text_to_html

    # Report progress
    if progress_callback:
        progress_callback(completed_batches, len(batched_tasks),
                         f"Completed {completed_batches}/{len(batched_tasks)} batches. Processing results...")

        # Track processed rows for progress reporting
        processed_rows = 0
        total_rows = len(all_results)

        # Process all results
        for result in all_results:
            # Get task ID and index
            task_id = result['custom_id']
            idx = int(task_id.split('-')[1])

            # Update progress with row count
            processed_rows += 1
            if progress_callback:
                progress_callback(processed_rows, total_rows,
                                 f"Processing email {processed_rows}/{total_rows} (row {idx})...")

            # Get response content
            try:
                response_body = result['response']['body']

                if not isinstance(response_body, dict):
                    response_body = json.loads(response_body) if isinstance(response_body, str) else {}

                choices = response_body.get('choices', [])

                if not choices:
                    raise ValueError("No choices in response")

                message = choices[0].get('message', {})
                content = message.get('content', '')

                # Handle different content types
                if isinstance(content, (int, float)):
                    content = str(content)

                response_content = json.loads(content) if content else {}

                # Extract email content
                email_subject = response_content.get('subject', '')
                email_content = response_content.get('content', '')
                email_preheader = response_content.get('preheader', '')

                # Format the email in the expected format for validation
                formatted_email = f"""Email Content:
Subject: {email_subject}
Pre-header: {email_preheader}
{email_content}"""

                # Validate email using guardrails if available
                valid = True
                validation_message = ""
                if validate_email is not None:
                    try:
                        # Get product name
                        product_name = user_product_mapping[idx]['product'].get('Product_Name', '')

                        # Validate email
                        validation_result = validate_email(
                            email_body=formatted_email,
                            product_name=product_name,
                            run_safety_check=True,
                            run_template_check=False,
                            run_feature_check=False
                        )

                        valid = validation_result['overall_valid']

                        # If not valid, regenerate
                        if not valid:
                            # Collect validation messages
                            instruction = ""
                            for _, validation_value in validation_result['validation_results'].items():
                                if "is_valid" in validation_value and validation_value['is_valid'] == False:
                                    instruction += f"{validation_value['message']}\n"
                                if "is_safe" in validation_value and validation_value["is_safe"] == False:
                                    instruction += f"Not safe NSFW content: {validation_value['message']}\n"

                            # Add validation message to user message
                            validation_message = instruction

                            # Try to regenerate with validation feedback
                            retry_message = user_message + f"\n\nPlease fix the following issues:\n{instruction}"

                            # Note: We'll make a direct API call instead of creating a batch task for the retry

                            # Make a direct API call for the retry
                            try:
                                if progress_callback:
                                    progress_callback(processed_rows, total_rows,
                                                     f"Regenerating email {processed_rows}/{total_rows} (row {idx})...")

                                retry_response = client.chat.completions.create(
                                    model="ft:gpt-4o-mini-2024-07-18:analytics-vidhya:oev1mailgen:B7xdiKXj",
                                    temperature=0.7,
                                    response_format={"type": "json_object"},
                                    messages=[
                                        {
                                            "role": "system",
                                            "content": system_prompt
                                        },
                                        {
                                            "role": "user",
                                            "content": retry_message
                                        }
                                    ]
                                )

                                # Extract the retry response
                                retry_content = json.loads(retry_response.choices[0].message.content)

                                # Update with retry content
                                email_subject = retry_content.get('subject', email_subject)
                                email_content = retry_content.get('content', email_content)
                                email_preheader = retry_content.get('preheader', email_preheader)

                                # Add validation info
                                validation_message = "Email was regenerated to fix validation issues."
                            except Exception as e:
                                print(f"Error during retry for task {task_id}: {str(e)}")
                                validation_message = f"Failed to regenerate email: {str(e)}"
                    except Exception as e:
                        print(f"Error during validation for task {task_id}: {str(e)}")
                        validation_message = f"Validation error: {str(e)}"

                # Update DataFrame with email content
                data_df.at[idx, 'Subject'] = email_subject
                data_df.at[idx, 'Mail_Content'] = email_content
                data_df.at[idx, 'Preheader'] = email_preheader
                data_df.at[idx, 'Matched_Product'] = user_product_mapping[idx]['product'].get('Product_Name', '')
                data_df.at[idx, 'Similarity_Score'] = round(float(user_product_mapping[idx]['similarity']) * 100, 2)
                data_df.at[idx, 'Validation_Message'] = validation_message
                
                # Get template name from the task metadata if available
                task_id = result['custom_id']
                original_task = next((t for t in tasks if t['custom_id'] == task_id), None)
                template_name = 'AI_Generated'  # Default fallback
                
                # Extract template name from the original task metadata if available
                if original_task and 'metadata' in original_task:
                    stored_template_name = original_task['metadata'].get('template_name')
                    if stored_template_name:  # Only use if it's not None or empty string
                        template_name = stored_template_name
                        
                data_df.at[idx, 'Template_Name'] = template_name

                # Generate HTML in the same loop
                try:
                    if progress_callback:
                        progress_callback(processed_rows, total_rows,
                                         f"Generating HTML for email {processed_rows}/{total_rows} (row {idx})...")

                    # Get matched product
                    matched_product = user_product_mapping[idx]['product']

                    # Create email content dictionary
                    email_content_dict = {
                        'subject': email_subject,
                        'content': email_content,
                        'preheader': email_preheader
                    }

                    # Create settings for HTML conversion
                    settings = {
                        "sender_name": matched_product.get('Company_Name', 'OpenEngage Team'),
                        "style": "friendly",
                        "length": "medium",
                        "utm_source": "mass_email",
                        "utm_medium": "email",
                        "utm_campaign": "personalized_campaign",
                        "utm_content": data_df.at[idx, 'user_stage'].lower().replace(" ", "_"),
                        "template_context": {
                            "base_template": {
                                "product_data": matched_product
                            }
                        }
                    }

                    # Get the correct row for this specific user (idx)
                    user_row = data_df.loc[idx]

                    # Add organization URL to settings
                    org_url = matched_product.get('Company_URL') or matched_product.get('organization_url')
                    if org_url:
                        settings['organization_url'] = org_url

                    # Convert to HTML with the correct user's data
                    html_content = text_to_html(
                        email_content_dict,
                        product_url=matched_product.get('Product_URL'),
                        product_name=matched_product.get('Product_Name'),
                        communication_settings=settings,
                        recipient_email=user_row.get('user_email') if hasattr(user_row, 'get') else user_row['user_email'],
                        recipient_first_name=user_row.get('first_name') if hasattr(user_row, 'get') else user_row['first_name']
                    )

                    # Add HTML content to the DataFrame
                    data_df.at[idx, 'HTML_Content'] = html_content

                except Exception as e:
                    data_df.at[idx, 'HTML_Content'] = f"<p>Error generating HTML: {str(e)}</p>"

            except Exception as e:
                print(f"Error processing result for task {task_id}: {str(e)}")
    else:
        # If the job is not completed, use the sequential approach as fallback
        from core.mass_email_generator import process_single_user
        from core.email_formatter import text_to_html

        # Report progress
        if progress_callback:
            progress_callback(0, len(data_df), "Batch job not completed. Using sequential approach...")

        # Process users one by one
        total_users = len(data_df)
        for idx, row in data_df.iterrows():
            # Update progress
            if progress_callback:
                progress_callback(idx, total_users, f"Processing user {idx+1}/{total_users}: {row['first_name']}")

            try:
                # Process user
                result = process_single_user(row, products)

                # Update DataFrame with email content
                data_df.at[idx, 'Subject'] = result['Subject']
                data_df.at[idx, 'Mail_Content'] = result['Mail_Content']
                data_df.at[idx, 'Matched_Product'] = result['Matched_Product']
                data_df.at[idx, 'Similarity_Score'] = round(float(result['Similarity_Score']) * 100, 2)
                data_df.at[idx, 'Template_Name'] = result.get('Template_Name', 'Fallback_Generated')

                # Generate HTML in the same loop
                if progress_callback:
                    progress_callback(idx, total_users, f"Generating HTML for user {idx+1}/{total_users}: {row['first_name']}")

                # Get matched product
                matched_product_name = result['Matched_Product']
                matched_product = next((p for p in products if p.get('Product_Name') == matched_product_name), products[0])

                # Create email content dictionary
                email_content_dict = {
                    'subject': result['Subject'],
                    'content': result['Mail_Content'],
                    'preheader': result.get('Preheader', '')
                }

                # Create settings for HTML conversion
                settings = {
                    "sender_name": matched_product.get('Company_Name', 'OpenEngage Team'),
                    "style": "friendly",
                    "length": "medium",
                    "utm_source": "mass_email",
                    "utm_medium": "email",
                    "utm_campaign": "personalized_campaign",
                    "utm_content": row['user_stage'].lower().replace(" ", "_"),
                    "template_context": {
                        "base_template": {
                            "product_data": matched_product
                        }
                    }
                }

                # Add organization URL to settings
                org_url = matched_product.get('Company_URL') or matched_product.get('organization_url')
                if org_url:
                    settings['organization_url'] = org_url

                # Convert to HTML
                html_content = text_to_html(
                    email_content_dict,
                    product_url=matched_product.get('Product_URL'),
                    product_name=matched_product.get('Product_Name'),
                    communication_settings=settings,
                    recipient_email=row.get('user_email'),
                    recipient_first_name=row.get('first_name')
                )

                # Add HTML content to the DataFrame
                data_df.at[idx, 'HTML_Content'] = html_content

            except Exception as e:
                print(f"Error processing user {row['first_name']}: {str(e)}")
                data_df.at[idx, 'Validation_Message'] = f"Error: {str(e)}"

    # Generate HTML for any rows that don't have it yet
    # Import required functions for HTML conversion if not already imported
    if 'text_to_html' not in locals():
        from core.email_formatter import text_to_html

    # Report progress
    if progress_callback:
        progress_callback(0, len(data_df), "Checking for missing HTML content...")

    # Count rows that need HTML generation
    rows_needing_html = [idx for idx, row in data_df.iterrows()
                        if 'HTML_Content' not in row or not row['HTML_Content']]

    if rows_needing_html:
        if progress_callback:
            progress_callback(0, len(rows_needing_html),
                             f"Generating HTML for {len(rows_needing_html)} remaining rows...")

        # Process each row that needs HTML
        for i, idx in enumerate(rows_needing_html):
            try:
                row = data_df.loc[idx]

                # Skip if no content
                if not row.get('Subject') or not row.get('Mail_Content'):
                    continue

                # Update progress
                if progress_callback:
                    progress_callback(i, len(rows_needing_html),
                                     f"Generating HTML for row {i+1}/{len(rows_needing_html)}")

                # Get matched product
                if idx not in user_product_mapping:
                    # This shouldn't happen, but just in case
                    print(f"Warning: No product mapping found for row {idx}")
                    continue

                matched_product = user_product_mapping[idx]['product']

                # Create email content dictionary
                email_content_dict = {
                    'subject': row['Subject'],
                    'content': row['Mail_Content'],
                    'preheader': row.get('Preheader', '')
                }

                # Create settings for HTML conversion
                settings = {
                    "sender_name": matched_product.get('Company_Name', 'OpenEngage Team'),
                    "style": "friendly",
                    "length": "medium",
                    "utm_source": "mass_email",
                    "utm_medium": "email",
                    "utm_campaign": "personalized_campaign",
                    "utm_content": row['user_stage'].lower().replace(" ", "_"),
                    "template_context": {
                        "base_template": {
                            "product_data": matched_product
                        }
                    }
                }

                # Add organization URL to settings
                org_url = matched_product.get('Company_URL') or matched_product.get('organization_url')
                if org_url:
                    settings['organization_url'] = org_url

                # Convert to HTML
                html_content = text_to_html(
                    email_content_dict,
                    product_url=matched_product.get('Product_URL'),
                    product_name=matched_product.get('Product_Name'),
                    communication_settings=settings,
                    recipient_email=row.get('user_email'),
                    recipient_first_name=row.get('first_name')
                )

                # Add HTML content to the DataFrame
                data_df.at[idx, 'HTML_Content'] = html_content

            except Exception as e:
                print(f"Error generating HTML for row {idx}: {str(e)}")
                data_df.at[idx, 'HTML_Content'] = f"<p>Error generating HTML: {str(e)}</p>"

    # Save HTML emails
    # Report progress
    if progress_callback:
        progress_callback(0, len(data_df), "Saving HTML emails...")

    # Create directory for HTML emails
    os.makedirs("data/html_emails", exist_ok=True)

    # Generate timestamp for filenames
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save HTML emails (only first 5 for reference)
    try:
        # Get the first 5 rows with HTML content
        rows_to_save = []
        count = 0
        total_saved = 0

        # Find rows with HTML content
        for idx, row in data_df.iterrows():
            if 'HTML_Content' in row and row['HTML_Content'] and isinstance(row['HTML_Content'], str):
                rows_to_save.append((idx, row))
                count += 1
                if count >= 5:  # Only save the first 5 emails as examples
                    break

        # Save the HTML files
        for i, (idx, row) in enumerate(rows_to_save):
            if progress_callback:
                progress_callback(i, len(rows_to_save),
                                 f"Saving HTML file {i+1}/{len(rows_to_save)} for user {row.get('first_name', idx)}")

            try:
                email_filename = f"data/html_emails/mass_email_{timestamp}_{idx}.html"
                with open(email_filename, 'w') as f:
                    f.write(row['HTML_Content'])
                total_saved += 1
            except Exception as e:
                print(f"Error saving HTML file for user {row.get('first_name', idx)}: {str(e)}")

        # Final progress update
        if progress_callback:
            progress_callback(len(rows_to_save), len(rows_to_save),
                             f"Saved {total_saved} HTML files as examples")
    except Exception as e:
        print(f"Error in HTML saving: {str(e)}")

    return data_df
