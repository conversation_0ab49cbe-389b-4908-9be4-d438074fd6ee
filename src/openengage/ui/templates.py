"""
Template generation and verification UI components for OpenEngage.
"""
import os
import json
import glob
import streamlit as st
import pandas as pd
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from openai import OpenAI
from dotenv import load_dotenv
from utils.file_utils import load_communication_settings, load_product_details, save_communication_settings, get_all_products

# Load environment variables
load_dotenv()

# Import WhatsApp template manager
try:
    from core.whatsapp_template_manager import WhatsAppTemplateManager
except ImportError:
    try:
        from openengage.core.whatsapp_template_manager import WhatsAppTemplateManager
    except ImportError:
        # This will be handled in the function
        pass

def display_templates_generator():
    """Display the templates generator interface"""
    st.write("## Templates Generator")

    # Create tabs for different template generation methods
    text_tab, html_tab = st.tabs(["Generate Text-Based Template", "Generate Template from HTML"])

    with text_tab:
        display_text_based_template_generator()

    with html_tab:
        display_html_template_generator()

def display_text_based_template_generator():
    """Display the text-based template generator interface"""

    # 1. Product Selection
    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load all available products with optional filtering
    products_list = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)

    if not products_list:
        if org_filter_enabled and org_url:
            st.warning(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
        else:
            st.warning("No products found. Please add products first.")
        return

    # Create product selection dropdown
    product_names = [p.get("Product_Name", "") for p in products_list]
    selected_product = st.selectbox("Select Product", product_names, key="text_product_select")

    if selected_product:
        # Get selected product details
        product_data = next((p for p in products_list if p.get("Product_Name") == selected_product), None)
        if product_data:
            st.write("### Product Details")
            with st.expander("View Product Details", expanded=False):
                st.json(product_data)

            # 2. Communication Settings
            st.write("### Communication Settings")
            # Try to get settings by company name first, then by URL
            company_name = product_data.get("Company_Name", "")
            company_url = product_data.get("Company_URL", "") or product_data.get("organization_url", "")
            
            # Try first with company name, then with URL
            comm_settings = load_communication_settings(sender_name=company_name)
            
            # If not found by name, try by URL
            if not comm_settings.get("organization_url") and company_url:
                url_settings = load_communication_settings(organization_url=company_url)
                if url_settings.get("organization_url") == company_url:
                    comm_settings = url_settings
            
            # For debugging, show what settings were loaded
            st.caption(f"Loaded settings for: {comm_settings.get('sender_name', 'Default')}")

            # Allow user to modify settings
            with st.expander("Modify Communication Settings"):
                col1, col2 = st.columns(2)
                with col1:
                    modified_settings = dict(comm_settings)

                    # Get brand personality and tone from communication settings
                    brand_personality = comm_settings.get("brand_personality", "")
                    tone_of_voice = comm_settings.get("tone_of_voice", "")

                    # Create a list of standard communication styles
                    standard_styles = ["friendly", "persuasive", "authoritative", "direct", "informative", "conversational"]

                    # Add all brand tone of voice values if available
                    style_options = standard_styles.copy()
                    brand_tone_options = []
                    tone_descriptors = []

                    if tone_of_voice and tone_of_voice.strip():
                        # Split tone of voice by commas and process each tone
                        tone_descriptors = [tone.strip().lower() for tone in tone_of_voice.split(',') if tone.strip()]

                        # Add each unique tone descriptor to the options
                        for tone_descriptor in tone_descriptors:
                            if tone_descriptor:
                                # Check if this tone is already in standard styles (case insensitive)
                                is_standard = False
                                standard_index = -1
                                for i, std_style in enumerate(standard_styles):
                                    if tone_descriptor.lower() == std_style.lower():
                                        is_standard = True
                                        standard_index = i
                                        break

                                # Check if already added to brand tone options
                                if tone_descriptor.lower() not in [b.lower().replace("✨ ", "").replace(" (brand tone)", "") for b in brand_tone_options]:
                                    if is_standard:
                                        # Replace the standard style with the highlighted version
                                        standard_styles[standard_index] = f"✨ {standard_styles[standard_index]} (Brand Tone)"
                                    else:
                                        # Add as a new brand tone option
                                        brand_tone_option = f"✨ {tone_descriptor.capitalize()} (Brand Tone)"
                                        brand_tone_options.append(brand_tone_option)

                        # Create the final style options list with highlighted standard styles
                        style_options = brand_tone_options.copy()

                        # Add all standard styles (some may now be highlighted)
                        for style in standard_styles:
                            style_options.append(style)

                    # Determine the index for the style selectbox
                    current_style = comm_settings.get("style", "friendly")

                    # Find the best matching style option
                    style_index = 0  # Default to first option

                    # First, check for exact matches (case-insensitive)
                    for i, option in enumerate(style_options):
                        # Strip the brand tone markers for comparison
                        clean_option = option.replace("✨ ", "").replace(" (Brand Tone)", "").lower()
                        if current_style.lower() == clean_option:
                            style_index = i
                            break

                    # If no exact match found, check if it's a brand tone that should be highlighted
                    if style_index == 0 and tone_descriptors:
                        for tone in tone_descriptors:
                            if current_style.lower() == tone.lower():
                                # It's a brand tone, find the highlighted version
                                for i, option in enumerate(style_options):
                                    if "✨" in option and tone.lower() in option.lower():
                                        style_index = i
                                        break
                                break

                    # Display style options with brand tone highlighted
                    style = st.selectbox(
                        "Communication Style",
                        style_options,
                        index=style_index,
                        help="Select a communication style for your messages"
                    )

                    # Store the actual style value without the "✨ (Brand Tone)" suffix
                    actual_style = style.split(' (Brand Tone)')[0].replace('✨ ', '') if '(Brand Tone)' in style else style
                    # Ensure proper capitalization for brand tones (lowercase for standard styles)
                    if '(Brand Tone)' in style:
                        actual_style = actual_style.lower()

                    modified_settings["style"] = actual_style

                    modified_settings["sender_name"] = st.text_input(
                        "Sender Name",
                        value=comm_settings.get("sender_name", product_data.get("Company_Name", ""))
                    )
                    modified_settings["utm_source"] = st.text_input(
                        "UTM Source",
                        value=comm_settings.get("utm_source", "email")
                    )
                    modified_settings["utm_campaign"] = st.text_input(
                        "UTM Campaign",
                        value=comm_settings.get("utm_campaign", "product_launch")
                    )
                    
                    # Remove emoji toggle from here

                with col2:
                    modified_settings["length"] = st.selectbox(
                        "Email Length",
                        ["<100 words", "100-150 words", "150-200 words", ">200 words"],
                        index=["<100 words", "100-150 words", "150-200 words", ">200 words"].index(
                            comm_settings.get("length", "100-150 words")) if comm_settings.get("length", "100-150 words") in
                            ["<100 words", "100-150 words", "150-200 words", ">200 words"] else 1
                    )

                    # Brand personality from communication settings
                    brand_personality_input = st.text_area(
                        "Brand Personality",
                        value=brand_personality,
                        help="Key personality traits of your brand (e.g., Innovative, Trustworthy, Bold)",
                        height=80
                    )
                    modified_settings["brand_personality"] = brand_personality_input

                    # Display brand tone as read-only with a note that it's used in Communication Style
                    st.markdown(f"""
                    <div style="border-left: 3px solid #8D06FE; padding-left: 10px; margin-top: 16px;">
                        <p style="margin-bottom: 5px; font-weight: bold;">Brand Tone of Voice</p>
                        <p style="margin-top: 0; font-style: italic;">{tone_of_voice}</p>
                        <p style="margin-top: 5px; font-size: 0.8rem; color: #666;">
                            ✨ Brand tone is available as a Communication Style option
                        </p>
                    </div>
                    """, unsafe_allow_html=True)

                    # Keep the tone of voice from the original settings
                    modified_settings["tone_of_voice"] = tone_of_voice

                    # Get utm_medium with default to Email, then ensure it's in the allowed list
                    utm_medium = comm_settings.get("utm_medium", "Email")
                    valid_mediums = ["Email", "WhatsApp", "SMS"]
                    
                    # Default to index 0 (Email) if the value isn't in the list
                    try:
                        medium_index = valid_mediums.index(utm_medium)
                    except ValueError:
                        medium_index = 0  # Default to Email if not found
                    
                    modified_settings["utm_medium"] = st.selectbox(
                        "UTM Medium",
                        valid_mediums,
                        index=medium_index
                    )
                    modified_settings["utm_content"] = st.text_input(
                        "UTM Content",
                        value=comm_settings.get("utm_content", "initial")
                    )
                    
                    # Remove emoji toggle from here

                # Save button for communication settings
                if st.button("Save Communication Settings"):
                    # Get organization URL from product data
                    org_url = product_data.get("Company_URL", "") or product_data.get("organization_url", "")
                    sender_name = modified_settings.get("sender_name", product_data.get("Company_Name", ""))
                    
                    # Ensure utm_medium is capitalized
                    if "utm_medium" in modified_settings:
                        utm_medium = modified_settings["utm_medium"]
                        if utm_medium.lower() == "email":
                            modified_settings["utm_medium"] = "Email"
                        elif utm_medium.lower() == "whatsapp":
                            modified_settings["utm_medium"] = "WhatsApp"
                        elif utm_medium.lower() == "sms":
                            modified_settings["utm_medium"] = "SMS"
                    
                    # Save communication settings with organization URL
                    save_communication_settings(
                        settings=modified_settings,
                        sender_name=sender_name,
                        organization_url=org_url
                    )
                    
                    # Save settings to session state for use in email generation
                    st.session_state.modified_comm_settings = modified_settings
                    st.success(f"Communication settings saved successfully for {sender_name}!")
                    
                    # Force reload
                    st.rerun()
                    
                    # Add note about template tags
                    st.info(
                        "Template tags will be automatically generated for each template to provide a quick understanding of its purpose and content."
                    )
            
            # 3. Template Customization Options (separate from Communication Settings)
            st.write("### Template Customization")
            
            # Create columns for emoji toggles
            emoji_col1, emoji_col2 = st.columns(2)
            
            with emoji_col1:
                # Toggle for emojis in subject line
                use_emojis_subject = st.toggle(
                    "Include Emojis in Subject Line",
                    value=comm_settings.get("use_emojis_subject", False),
                    help="Enable to add relevant emojis to the email subject line",
                    key="emoji_subject_toggle"
                )
            
            with emoji_col2:
                # Toggle for emojis in email body
                use_emojis_body = st.toggle(
                    "Include Emojis in Email Body",
                    value=comm_settings.get("use_emojis_body", False),
                    help="Enable to add relevant emojis throughout the email body content",
                    key="emoji_body_toggle"
                )
            
            # Store emoji preferences in session state
            if "modified_comm_settings" not in st.session_state:
                st.session_state.modified_comm_settings = {}
            
            # Store both settings separately
            st.session_state.modified_comm_settings["use_emojis_subject"] = use_emojis_subject
            st.session_state.modified_comm_settings["use_emojis_body"] = use_emojis_body
            
            # Keep backward compatibility with the old single setting
            st.session_state.modified_comm_settings["use_emojis"] = (use_emojis_subject or use_emojis_body)

            # 4. Template Generation
            num_templates = st.number_input("Number of Templates per Stage", min_value=1, max_value=10, value=5)

            if st.button("Generate Templates"):
                with st.spinner("Generating templates..."):
                    # Update session state with current product
                    st.session_state.current_product_data = product_data
                    # Generate templates
                    from core.email_generator import generate_templates
                    generate_templates(num_templates,product_data)
                    st.session_state.show_templates_generator = False
                    st.session_state.show_template_verification = True
                    st.rerun()

def generate_cta_options(template_data, num_options=4):
    """Generate CTA options for a single template using GPT-4o-mini

    Args:
        template_data (dict): Template data containing template content
        num_options (int): Number of CTA options to generate (1 for auto-generation, 4 for regeneration)

    Returns:
        list: List of CTA options
    """
    try:
        print(f"DEBUG: Generating CTA options for template: {template_data.get('template_name', 'Unknown')}")

        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Extract email body for context
        email_body = template_data.get('template', {}).get('body', '')
        email_subject = template_data.get('template', {}).get('subject', '')
        product_name = template_data.get('product_data', {}).get('Product_Name', '')

        print(f"DEBUG: Processing template for product: {product_name}")

        # Create prompt for CTA generation
        prompt = f"""
        Analyze this email content and generate {num_options} compelling Call-to-Action (CTA) button text{"s" if num_options > 1 else ""}.

        Email Subject: {email_subject}

        Email Body (FOCUS ON THIS): {email_body}

        Product: {product_name}

        Instructions:
        - Carefully read the email body content to understand the main message and desired user action
        - Create short, action-oriented CTA text (2-4 words maximum)
        - Make each CTA compelling and directly relevant to what the email body is promoting
        - Focus on the specific benefits or actions mentioned in the email body
        {"- Generate " + str(num_options) + " different variations that could appeal to different user motivations" if num_options > 1 else "- Generate 1 compelling CTA that best matches the email content"}
        - IMPORTANT: Do NOT include any single quotes (') or double quotes (") in your response
        - IMPORTANT: Do NOT use quotation marks of any kind around the CTA text
        - IMPORTANT: Do NOT use the word "Unlock" in any CTA text
        - Use action verbs that match the email's tone and purpose

        Examples of good CTAs: Get Started, Learn More, Join Now, Start Free Trial, Download Now, Explore Features, Book Demo, Sign Up Today, Claim Offer, View Details

        Return exactly {num_options} CTA option{"s" if num_options > 1 else ""}, one per line, with no quotes, no numbering, no bullet points, no extra text or punctuation.
        """

        # Generate CTA using GPT-4o-mini
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "system",
                    "content": "You are a marketing expert specializing in creating compelling call-to-action buttons. Analyze email content carefully and generate short, action-oriented CTA text. NEVER use single quotes, double quotes, or any quotation marks in your response. Focus on the email body content to understand the main action the email wants users to take."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.8,
            max_tokens=50
        )

        cta_response = response.choices[0].message.content.strip()

        # Parse the response to get individual CTAs
        cta_lines = [line.strip() for line in cta_response.split('\n') if line.strip()]

        # Clean up any quotes that might have been generated
        cta_options = []
        for cta in cta_lines:
            # Remove any quotes and clean up
            cleaned_cta = cta.replace('"', '').replace("'", '').strip()
            # Remove numbering if present (e.g., "1. Get Started" -> "Get Started")
            cleaned_cta = cleaned_cta.split('. ', 1)[-1] if '. ' in cleaned_cta else cleaned_cta
            if cleaned_cta:
                cta_options.append(cleaned_cta)

        # Ensure we have exactly the requested number of options
        while len(cta_options) < num_options:
            fallback_ctas = ["Learn More", "Get Started", "Join Now", "Explore Now"]
            for fallback in fallback_ctas:
                if fallback not in cta_options:
                    cta_options.append(fallback)
                    break

        # Take only the requested number of options
        cta_options = cta_options[:num_options]

        print(f"DEBUG: Generated CTA options for {template_data.get('template_name', 'Unknown')}: {cta_options}")

        return cta_options

    except Exception as e:
        print(f"DEBUG: Error generating CTA options for template {template_data.get('template_name', 'Unknown')}: {str(e)}")
        fallback_ctas = ["Learn More", "Get Started", "Join Now", "Explore Now"]
        return fallback_ctas[:num_options]  # Return appropriate number of fallback CTAs

def save_template_cta(template_name, cta_text):
    """Save CTA for a specific template

    Args:
        template_name (str): Name of the template
        cta_text (str): CTA text to save

    Returns:
        bool: True if save successful, False otherwise
    """
    try:
        print(f"DEBUG: Saving CTA for template {template_name}: {cta_text}")

        # Load existing CTAs
        cta_file_path = 'data/templates/template_ctas.json'
        ctas = {}

        if os.path.exists(cta_file_path):
            try:
                with open(cta_file_path, 'r') as f:
                    ctas = json.load(f)
            except Exception as e:
                print(f"DEBUG: Error loading existing CTAs: {str(e)}")
                ctas = {}

        # Update with new CTA
        ctas[template_name] = cta_text

        # Ensure data/templates directory exists
        os.makedirs('data/templates', exist_ok=True)

        # Save updated CTAs
        with open(cta_file_path, 'w') as f:
            json.dump(ctas, f, indent=4)

        print(f"DEBUG: Successfully saved CTA for {template_name}")
        return True

    except Exception as e:
        print(f"DEBUG: Error saving CTA for template {template_name}: {str(e)}")
        return False

def load_template_ctas():
    """Load CTAs from the JSON file

    Returns:
        dict: Dictionary mapping template names to CTA texts
    """
    cta_file_path = 'data/templates/template_ctas.json'
    try:
        if os.path.exists(cta_file_path):
            with open(cta_file_path, 'r') as f:
                ctas = json.load(f)
            print(f"DEBUG: Loaded {len(ctas)} CTAs from {cta_file_path}")
            return ctas
        else:
            print("DEBUG: CTA file does not exist")
            return {}
    except Exception as e:
        print(f"DEBUG: Error loading CTAs from file: {str(e)}")
        return {}

def delete_template(template_file, idx, templates):
    """Delete a template from the template file
    
    Args:
        template_file (str): Path to the template file
        idx (int): Index of the template to delete
        templates (list): List of templates
        
    Returns:
        bool: True if deletion was successful, False otherwise
    """
    try:
        # Remove the template from the list
        if idx < len(templates):
            deleted_template = templates.pop(idx)
            
            # Save the updated templates list back to the file
            with open(template_file, 'w') as f:
                json.dump(templates, f, indent=4)
                
            print(f"Deleted template: {deleted_template.get('template_name', 'Unknown')}")
            return True
        return False
    except Exception as e:
        print(f"Error deleting template: {str(e)}")
        return False

def display_html_template_generator():
    """Display the HTML template generator interface"""

    st.write("### Generate Template from HTML")
    st.write("Select an HTML template and product to generate personalized content.")

    # Load HTML templates from CSV
    try:
        html_templates_df = pd.read_csv('data/html_templates.csv')

        # Create dropdown for HTML template selection
        template_options = [f"Template {row['No']}" for _, row in html_templates_df.iterrows()]
        selected_template_option = st.selectbox("Select HTML Template", template_options, key="html_template_select")

        if selected_template_option:
            # Extract template number
            template_no = int(selected_template_option.split()[-1])
            selected_html = html_templates_df[html_templates_df['No'] == template_no]['Mail'].iloc[0]

            # Show preview of selected HTML template
            with st.expander("Preview Selected HTML Template"):
                st.code(selected_html[:500] + "..." if len(selected_html) > 500 else selected_html, language="html")

            # Product Selection
            st.write("### Product Selection")

            # Check if organization products visibility is enabled
            org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

            # Get organization URL from session state if available
            org_url = None
            if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
                org_url = st.session_state.current_user.get('organization', {}).get('url', None)

            # Load all available products with optional filtering
            products_list = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)

            if not products_list:
                if org_filter_enabled and org_url:
                    st.warning(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
                else:
                    st.warning("No products found. Please add products first.")
                return

            # Create product selection dropdown
            product_names = [p.get("Product_Name", "") for p in products_list]
            selected_product = st.selectbox("Select Product", product_names, key="html_product_select")

            if selected_product:
                # Get selected product details
                product_data = next((p for p in products_list if p.get("Product_Name") == selected_product), None)

                if product_data:
                    # Show product details
                    with st.expander("View Product Details", expanded=False):
                        st.json(product_data)

                    # Generate button
                    if st.button("Generate HTML Template", type="primary", key="generate_html_template"):
                        with st.spinner("Generating personalized HTML template..."):
                            try:
                                # Import the HTML template generator
                                from ui.template_from_html import run_email_generation

                                # Convert product data to string format
                                product_info = f"""
Product: {product_data.get('Product_Name', '')}
Company: {product_data.get('Company_Name', '')}
Product URL: {product_data.get('Product_URL', '')}
Type: {product_data.get('Type_of_Product', '')}
Features: {', '.join(product_data.get('Product_Features', []))}
Summary: {product_data.get('Product_Summary', '')}
Priority: {product_data.get('Priority', '')}
Cross Sell Product: {product_data.get('Cross_Sell_Product', '')}
"""

                                # Generate the HTML template
                                result = run_email_generation(selected_html, product_info)

                                # Display the result
                                st.success("HTML template generated successfully!")

                                # Show the generated HTML
                                st.write("### Generated HTML Template")

                                # Extract the final HTML from the result
                                if hasattr(result, 'pydantic') and hasattr(result.pydantic, 'updated_html'):
                                    final_html = result.pydantic.updated_html
                                elif hasattr(result, 'raw') and 'updated_html' in str(result.raw):
                                    # Try to extract HTML from raw output
                                    final_html = str(result.raw)
                                else:
                                    final_html = str(result)

                                # Display HTML in expandable sections
                                col1, col2 = st.columns(2)

                                with col1:
                                    st.write("**New HTML:**")
                                    st.code(final_html, language="html")

                                with col2:
                                    st.write("**Preview:**")
                                    st.components.v1.html(final_html, height=600, scrolling=True)

                                # Option to download the HTML
                                st.download_button(
                                    label="Download HTML Template",
                                    data=final_html,
                                    file_name=f"{selected_product}_template_{template_no}.html",
                                    mime="text/html"
                                )

                            except Exception as e:
                                st.error(f"Error generating HTML template: {str(e)}")
                                st.write("Please make sure the template_from_html module is properly configured.")

    except FileNotFoundError:
        st.error("HTML templates file not found. Please make sure 'data/html_templates.csv' exists.")
    except Exception as e:
        st.error(f"Error loading HTML templates: {str(e)}")

def display_template_verification():
    """Display the template verification interface"""
    st.write("## ✓ Template Verification")
    st.write("Review and verify generated email templates for each stage.")

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Get list of all template files
    all_template_files = glob.glob('data/templates/*.json')

    if not all_template_files:
        st.warning("No templates found. Please generate templates first.")
        return
        
    # Get all stages from journeys of the current organization's products
    org_stages = set()
    
    # Get all products for the current organization
    if org_filter_enabled and org_url:
        org_products = get_all_products(organization_url=org_url, filter_by_org=True)
        
        # For each product, load its user journey and extract stages
        for product in org_products:
            product_name = product.get('Product_Name')
            if product_name:
                # Import load_user_journey function
                from utils.file_utils import load_user_journey
                
                # Load journey for this product
                journey = load_user_journey(product_name)
                
                # Extract stages from journey
                for stage_entry in journey:
                    if isinstance(stage_entry, dict) and 'current_stage' in stage_entry:
                        # Convert stage name to match template filename format
                        stage_name = stage_entry['current_stage'].lower().replace(' ', '_')
                        org_stages.add(stage_name)
    
    # If no organization filter, or no stages found, use all template files
    if not org_filter_enabled or not org_url or not org_stages:
        template_files = all_template_files
        stage_names = [os.path.basename(f).replace('.json', '').replace('_', ' ').title() for f in template_files]
    else:
        # Filter template files to only include those for organization product stages
        template_files = []
        stage_names = []
        
        for template_file in all_template_files:
            basename = os.path.basename(template_file)
            stage = basename.replace('.json', '')
            
            if stage in org_stages:
                template_files.append(template_file)
                stage_names.append(stage.replace('_', ' ').title())
                
        if not template_files:
            st.warning(f"No templates found for stages in your organization's products.")
            return

    # Load existing CTAs
    template_ctas = load_template_ctas()
    print(f"DEBUG: Loaded {len(template_ctas)} existing CTAs")

    # Create tabs for filtered stages
    tabs = st.tabs(stage_names)

    # Display templates for each stage in its tab
    for tab, template_file in zip(tabs, template_files):
        with tab:
            try:
                with open(template_file, 'r') as f:
                    templates = json.load(f)

                # Filter templates by organization URL and organization's products if enabled
                if org_filter_enabled and org_url:
                    # Get all products for the current organization
                    org_products = get_all_products(organization_url=org_url, filter_by_org=True)
                    org_product_names = [p.get('Product_Name', '') for p in org_products if p.get('Product_Name', '')]
                    
                    # Filter templates by organization URL and product name
                    templates = [
                        t for t in templates
                        if (t.get('product_data', {}).get('Company_URL', '') == org_url and 
                            t.get('product_data', {}).get('Product_Name', '') in org_product_names)
                    ]
                
                # Exclude templates with pruning_flag = 1
                templates = [
                    t for t in templates
                    if t.get('performance', {}).get('pruning_flag', 0) != 1
                ]

                if not templates:
                    if org_filter_enabled and org_url:
                        st.warning(f"No templates found for this stage in your organization ({org_url}).")
                    else:
                        st.warning(f"No templates found for this stage.")
                    continue

                # Add "Verified" label once at the top
                col1, col2 = st.columns([9, 1])
                with col2:
                    st.write("✓ Verified")

                # Display each template
                for idx, template_data in enumerate(templates):
                    template = template_data.get('template', {})
                    template_name = template_data.get('template_name', f'Template {idx + 1}')

                    # Create a container for the row
                    row = st.container()

                    # Create columns with 9:1 ratio inside the container
                    with row:
                        col1, col2 = st.columns([9, 1])

                        # Main template expander in first column
                        with col1:
                            with st.expander(f"📧 {template_name}", expanded=False):
                                # Display template tags with custom styling
                                tags = template_data.get('tags', [])
                                # Filter out None values and empty strings from tags
                                tags = [tag for tag in tags if tag and tag != 'None']
                                if tags:
                                    st.write("**Tags:**")
                                    tags_html = ""
                                    # Use brand color for all tags
                                    brand_color = "#02C688"  # Brand color
                                    
                                    # Define emoji mappings for different tag types
                                    # Feature-related emojis
                                    feature_keywords = {
                                        "report": "📊", "dashboard": "📈", "chart": "📉", "analytics": "📊", "data": "📊",
                                        "ai": "🤖", "ml": "🤖", "intelligent": "🤖", "smart": "🤖", "automation": "⚙️", "auto": "⚙️", 
                                        "security": "🔒", "secure": "🔒", "protection": "🛡️", "privacy": "🔐",
                                        "chat": "💬", "message": "💬", "notification": "🔔", "alert": "🚨",
                                        "search": "🔍", "find": "🔍", "discover": "🔍",
                                        "cloud": "☁️", "storage": "🗄️", "backup": "💾",
                                        "performance": "⚡", "speed": "⚡", "fast": "⚡",
                                        "mobile": "📱", "responsive": "📱", "app": "📱",
                                        "video": "🎥", "audio": "🎵", "music": "🎵",
                                        "social": "👥", "community": "👥", "network": "🌐",
                                        "integration": "🔄", "connect": "🔌", "sync": "🔄",
                                        "payment": "💳", "subscription": "💲", "billing": "💵",
                                        "product": "📦", "item": "📦", "goods": "📦"
                                    }
                                    
                                    # Offer-related emojis
                                    offer_keywords = {
                                        "free": "🆓", "trial": "🆓", "discount": "💰", "sale": "🏷️", "deal": "🏷️",
                                        "limited": "⏱️", "exclusive": "🔑", "special": "✨", "vip": "🌟",
                                        "new": "🆕", "upgrade": "⬆️", "premium": "👑", "pro": "⭐",
                                        "bonus": "🎁", "gift": "🎁", "extra": "➕", "plus": "➕",
                                        "early": "🕐", "access": "🔓", "invite": "📨"
                                    }
                                    
                                    # Motivation-related emojis
                                    motivation_keywords = {
                                        "time": "⏰", "save": "💾", "quick": "⚡", "efficient": "⚡",
                                        "easy": "👌", "simple": "👌", "hassle": "👌", "convenient": "👌",
                                        "growth": "📈", "increase": "📈", "boost": "🚀", "improve": "📈",
                                        "success": "🏆", "achieve": "🏆", "win": "🏆", "goal": "🎯",
                                        "productivity": "📈", "effective": "✅", "efficient": "⚡",
                                        "solution": "💡", "solve": "🔧", "fix": "🔧",
                                        "roi": "💰", "revenue": "💰", "profit": "💰", "money": "💰",
                                        "cost": "💲", "budget": "💲", "affordable": "💲",
                                        "transform": "✨", "revolutionize": "💫", "innovate": "💡",
                                        "support": "🤝", "help": "🤝", "assist": "🤝"
                                    }
                                    
                                    # Default emojis based on position (in case no keyword match is found)
                                    default_emojis = ["⚙️", "🏷️", "🚀", "📌"]
                                    
                                    for i, tag in enumerate(tags):
                                        # Use brand color for all tags
                                        bg_color = brand_color
                                        # Set text color to white for better contrast on brand color
                                        text_color = "#ffffff"
                                        # Add subtle shadow effect for depth
                                        shadow = "box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);"
                                        # Make tag more prominent with hover effect
                                        hover = "transition: all 0.3s cubic-bezier(.25,.8,.25,1);"
                                        
                                        # Find an appropriate emoji for the tag
                                        emoji = ""
                                        tag_lower = tag.lower()
                                        
                                        # Check for feature keywords
                                        if i == 0 or any(keyword in tag_lower for keyword in feature_keywords):
                                            for keyword, kw_emoji in feature_keywords.items():
                                                if keyword in tag_lower:
                                                    emoji = kw_emoji
                                                    break
                                            if not emoji:
                                                emoji = default_emojis[0]  # Default feature emoji
                                        # Check for offer keywords
                                        elif i == 1 or any(keyword in tag_lower for keyword in offer_keywords):
                                            for keyword, kw_emoji in offer_keywords.items():
                                                if keyword in tag_lower:
                                                    emoji = kw_emoji
                                                    break
                                            if not emoji:
                                                emoji = default_emojis[1]  # Default offer emoji
                                        # Check for motivation keywords
                                        elif i == 2 or any(keyword in tag_lower for keyword in motivation_keywords):
                                            for keyword, kw_emoji in motivation_keywords.items():
                                                if keyword in tag_lower:
                                                    emoji = kw_emoji
                                                    break
                                            if not emoji:
                                                emoji = default_emojis[2]  # Default motivation emoji
                                        else:
                                            emoji = default_emojis[3]  # Default generic emoji
                                        
                                        # Add emoji to tag
                                        tag_with_emoji = f"{emoji} {tag}"
                                        
                                        tags_html += f"<span style='display:inline-block; background-color:{bg_color}; color:{text_color}; padding:5px 12px; margin:3px 6px 3px 0; border-radius:20px; font-size:0.9em; font-weight:500; {shadow} {hover}'>{tag_with_emoji}</span>"
                                    st.markdown(tags_html, unsafe_allow_html=True)
                                
                                # Indicate if template includes emojis (with specific details)
                                has_emojis_subject = template_data.get('has_emojis_subject', False)
                                has_emojis_body = template_data.get('has_emojis_body', False)
                                
                                # Use fallback for backward compatibility
                                if 'has_emojis_subject' not in template_data and 'has_emojis' in template_data:
                                    has_emojis = template_data.get('has_emojis', False)
                                    has_emojis_subject = has_emojis
                                    has_emojis_body = has_emojis
                                
                                if has_emojis_subject and has_emojis_body:
                                    st.markdown("<span style='color:#09ab3b; font-size:0.9em;'>✓ Emojis in subject & body</span>", unsafe_allow_html=True)
                                elif has_emojis_subject:
                                    st.markdown("<span style='color:#09ab3b; font-size:0.9em;'>✓ Emojis in subject</span>", unsafe_allow_html=True)
                                elif has_emojis_body:
                                    st.markdown("<span style='color:#09ab3b; font-size:0.9em;'>✓ Emojis in body</span>", unsafe_allow_html=True)

                                # Display CTA if available
                                template_name = template_data.get('template_name', '')
                                if template_name in template_ctas:
                                    cta_text = template_ctas[template_name]
                                    print(f"DEBUG: Displaying CTA for {template_name}: {cta_text}")

                                    # Create columns for CTA display and edit button
                                    cta_col1, cta_col2 = st.columns([4, 1])

                                    with cta_col1:
                                        st.markdown(f"""
                                        <div style='margin: 10px 0; padding: 8px 16px; background-color: #f0f8ff; border-left: 4px solid #1f77b4; border-radius: 4px;'>
                                            <span style='color: #1f77b4; font-weight: 600; font-size: 0.9em;'>🎯 CTA: </span>
                                            <span style='color: #333; font-size: 0.9em; background-color: #1f77b4; color: white; padding: 4px 12px; border-radius: 20px; font-weight: 500;'>{cta_text}</span>
                                        </div>
                                        """, unsafe_allow_html=True)

                                    with cta_col2:
                                        # Edit CTA button
                                        edit_cta_key = f"edit_cta_mode_{template_file}_{idx}"
                                        if edit_cta_key not in st.session_state:
                                            st.session_state[edit_cta_key] = False

                                        if st.button("✏️ Edit", key=f"edit_cta_btn_{template_file}_{idx}", help="Edit CTA text"):
                                            st.session_state[edit_cta_key] = not st.session_state[edit_cta_key]
                                            st.rerun()

                                    # Show edit interface if toggled
                                    if st.session_state.get(edit_cta_key, False):
                                        edited_cta = st.text_input(
                                            "Edit CTA:",
                                            value=cta_text,
                                            key=f"edit_cta_input_{template_file}_{idx}",
                                            help="Edit the CTA text"
                                        )

                                        edit_col1, edit_col2 = st.columns([1, 1])
                                        with edit_col1:
                                            if st.button("💾 Save", key=f"save_edit_cta_{template_file}_{idx}", type="primary"):
                                                if save_template_cta(template_name, edited_cta):
                                                    st.success(f"✅ CTA updated to '{edited_cta}'!")
                                                    template_ctas[template_name] = edited_cta
                                                    st.session_state[edit_cta_key] = False
                                                    st.rerun()
                                                else:
                                                    st.error("Failed to save CTA. Please try again.")

                                        with edit_col2:
                                            if st.button("❌ Cancel", key=f"cancel_edit_cta_{template_file}_{idx}"):
                                                st.session_state[edit_cta_key] = False
                                                st.rerun()
                                else:
                                    print(f"DEBUG: No CTA found for template: {template_name}")

                                st.write("---")
                                
                                # Editable subject and body
                                new_subject = st.text_input("Subject:",
                                                          value=template.get('subject', 'No subject'),
                                                          key=f"subject_{template_file}_{idx}")

                                new_body = st.text_area("Body:",
                                                      value=template.get('body', 'No body'),
                                                      height=200,
                                                      key=f"body_{template_file}_{idx}")

                                # Action buttons row
                                col_update, col_image, col_preview, col_cta, col_delete = st.columns([1, 1, 1, 1, 1])
                                
                                # Update button for subject and body
                                with col_update:
                                    if st.button("Update Template", key=f"update_{template_file}_{idx}"):
                                        template['subject'] = new_subject
                                        template['body'] = new_body
                                        template_data['template'] = template
                                        
                                        # Save updated template back to file
                                        with open(template_file, 'w') as f:
                                            json.dump(templates, f, indent=4)
                                            
                                        st.success("✅ Template updated successfully!")
                                
                                # Add Image button
                                with col_image:
                                    # Create a unique key for this template's image display state
                                    image_key = f"show_images_{template_file}_{idx}"
                                    
                                    # Initialize session state for image display if it doesn't exist
                                    if image_key not in st.session_state:
                                        st.session_state[image_key] = False
                                    
                                    # Track cursor position for image insertion
                                    cursor_key = f"cursor_pos_{template_file}_{idx}"
                                    
                                    # Store cursor position when Add Image is clicked
                                    # Since Streamlit doesn't directly track cursor position,
                                    # we'll track the last edited field and use it as our cursor position indicator
                                    if new_body != template.get('body', 'No body'):
                                        # Body was just edited, so store current cursor position
                                        # This is a simple heuristic based on text changes
                                        # Find the first position where old and new text differ
                                        old_body = template.get('body', '')
                                        for i in range(min(len(old_body), len(new_body))):
                                            if old_body[i] != new_body[i]:
                                                st.session_state[cursor_key] = i
                                                break
                                        else:
                                            # If texts match up to the shorter one, cursor is at the end of the shorter one
                                            st.session_state[cursor_key] = min(len(old_body), len(new_body))
                                    
                                    # Toggle button to show/hide thumbnails
                                    if st.button("🖼️ Add Image", key=f"add_image_{template_file}_{idx}"):
                                        st.session_state[image_key] = not st.session_state[image_key]
                                        st.rerun()
                                
                                # Preview Template button
                                with col_preview:
                                    # Create a unique key for this template's preview state
                                    preview_key = f"show_preview_{template_file}_{idx}"
                                    
                                    # Initialize session state for preview if it doesn't exist
                                    if preview_key not in st.session_state:
                                        st.session_state[preview_key] = False
                                    
                                    # Toggle button to show/hide preview
                                    if st.button("👁️ Preview", key=f"preview_{template_file}_{idx}"):
                                        st.session_state[preview_key] = not st.session_state[preview_key]
                                        st.rerun()

                                # Generate CTA button
                                with col_cta:
                                    # Create a unique key for this template's CTA generation state
                                    cta_key = f"show_cta_{template_file}_{idx}"

                                    # Initialize session state for CTA generation if it doesn't exist
                                    if cta_key not in st.session_state:
                                        st.session_state[cta_key] = False

                                    # Toggle button to show/hide CTA generation
                                    if st.button("🔄 Regenerate CTA", key=f"generate_cta_{template_file}_{idx}"):
                                        st.session_state[cta_key] = not st.session_state[cta_key]
                                        st.rerun()
                                
                                # Delete button that uses session state for confirmation
                                with col_delete:
                                    # Define a unique key for this template's deletion state
                                    delete_key = f"delete_confirmation_{template_file}_{idx}"
                                    
                                    # Initialize session state for this template if not exists
                                    if delete_key not in st.session_state:
                                        st.session_state[delete_key] = False
                                    
                                    # Show different buttons based on confirmation state
                                    if not st.session_state[delete_key]:
                                        # Show initial delete button
                                        if st.button("🗑️ Delete Template", key=f"delete_{template_file}_{idx}", type="secondary"):
                                            # Set confirmation state to true when clicked
                                            st.session_state[delete_key] = True
                                            st.rerun()
                                    else:
                                        # Show confirmation warning and buttons
                                        st.warning("Are you sure you want to delete this template?")
                                        
                                        # Yes/No buttons for confirmation
                                        if st.button("✓ Yes, Delete", key=f"confirm_{template_file}_{idx}", type="primary"):
                                            if delete_template(template_file, idx, templates):
                                                st.session_state[delete_key] = False  # Reset confirmation state
                                                st.success("Template deleted successfully!")
                                                st.rerun()
                                            else:
                                                st.error("Failed to delete template.")
                                        
                                        if st.button("✗ Cancel", key=f"cancel_{template_file}_{idx}"):
                                            st.session_state[delete_key] = False  # Reset confirmation state
                                            st.rerun()

                                # Show CTA generation interface if toggled
                                if st.session_state.get(f"show_cta_{template_file}_{idx}", False):
                                    st.write("---")
                                    st.write("**Generate Call-to-Action (CTA) for this template:**")

                                    # Generate CTA options
                                    if st.button("🎯 Generate CTA Options", key=f"gen_cta_options_{template_file}_{idx}"):
                                        with st.spinner("Generating CTA options..."):
                                            print(f"DEBUG: Generating CTA options for template {template_data.get('template_name', 'Unknown')}")
                                            cta_options = generate_cta_options(template_data, num_options=4)
                                            st.session_state[f"cta_options_{template_file}_{idx}"] = cta_options
                                            print(f"DEBUG: Generated options: {cta_options}")

                                    # Show CTA options if available
                                    if f"cta_options_{template_file}_{idx}" in st.session_state:
                                        cta_options = st.session_state[f"cta_options_{template_file}_{idx}"]
                                        st.write("**Select a CTA option:**")

                                        # Display CTA options as buttons
                                        cta_cols = st.columns(2)
                                        selected_cta = None

                                        for i, cta_option in enumerate(cta_options):
                                            with cta_cols[i % 2]:
                                                if st.button(f"{cta_option}", key=f"select_cta_{template_file}_{idx}_{i}", use_container_width=True):
                                                    selected_cta = cta_option
                                                    st.session_state[f"selected_cta_{template_file}_{idx}"] = cta_option
                                                    print(f"DEBUG: Selected CTA: {cta_option}")

                                        # Show selected CTA for editing
                                        if f"selected_cta_{template_file}_{idx}" in st.session_state:
                                            current_cta = st.session_state[f"selected_cta_{template_file}_{idx}"]
                                            st.write("**Edit and approve your CTA:**")

                                            # Editable CTA text
                                            edited_cta = st.text_input(
                                                "CTA Text:",
                                                value=current_cta,
                                                key=f"edit_cta_{template_file}_{idx}",
                                                help="Edit the CTA text if needed"
                                            )

                                            # Approve button
                                            col_approve, col_cancel = st.columns([1, 1])

                                            with col_approve:
                                                if st.button("✅ Approve CTA", key=f"approve_cta_{template_file}_{idx}", type="primary"):
                                                    template_name = template_data.get('template_name', '')
                                                    if save_template_cta(template_name, edited_cta):
                                                        st.success(f"✅ CTA '{edited_cta}' saved successfully!")
                                                        # Update the loaded CTAs
                                                        template_ctas[template_name] = edited_cta
                                                        # Clear the CTA generation state
                                                        st.session_state[f"show_cta_{template_file}_{idx}"] = False
                                                        if f"cta_options_{template_file}_{idx}" in st.session_state:
                                                            del st.session_state[f"cta_options_{template_file}_{idx}"]
                                                        if f"selected_cta_{template_file}_{idx}" in st.session_state:
                                                            del st.session_state[f"selected_cta_{template_file}_{idx}"]
                                                        st.rerun()
                                                    else:
                                                        st.error("Failed to save CTA. Please try again.")

                                            with col_cancel:
                                                if st.button("❌ Cancel", key=f"cancel_cta_{template_file}_{idx}"):
                                                    # Clear the CTA generation state
                                                    st.session_state[f"show_cta_{template_file}_{idx}"] = False
                                                    if f"cta_options_{template_file}_{idx}" in st.session_state:
                                                        del st.session_state[f"cta_options_{template_file}_{idx}"]
                                                    if f"selected_cta_{template_file}_{idx}" in st.session_state:
                                                        del st.session_state[f"selected_cta_{template_file}_{idx}"]
                                                    st.rerun()

                                    # Close CTA generation button
                                    if st.button("Close CTA Generation", key=f"close_cta_{template_file}_{idx}", type="secondary"):
                                        st.session_state[f"show_cta_{template_file}_{idx}"] = False
                                        st.rerun()

                                # Show template preview if toggled
                                if st.session_state.get(f"show_preview_{template_file}_{idx}", False):
                                    st.write("---")
                                    st.write("**Email Template Preview:**")
                                    
                                    # Create a styled container for the preview
                                    preview_container = st.container()
                                    with preview_container:
                                        # Add styling
                                        st.markdown("""
                                        <style>
                                        .email-preview {
                                            border: 1px solid #ddd;
                                            border-radius: 8px;
                                            padding: 20px;
                                            background-color: white;
                                            max-width: 800px;
                                            margin: 0 auto;
                                        }
                                        .email-subject {
                                            border-bottom: 1px solid #eee;
                                            padding-bottom: 10px;
                                            margin-bottom: 15px;
                                            color: #333;
                                            font-size: 1.5em;
                                            font-weight: bold;
                                        }
                                        .email-body {
                                            font-family: Arial, sans-serif;
                                            color: #333;
                                            line-height: 1.6;
                                        }
                                        </style>
                                        """, unsafe_allow_html=True)
                                        
                                        # Create preview with styled container
                                        with st.container():
                                            st.markdown('<div class="email-preview">', unsafe_allow_html=True)
                                            
                                            # Display subject
                                            subject = template.get('subject', 'No subject')
                                            st.markdown(f'<div class="email-subject">{subject}</div>', unsafe_allow_html=True)
                                            
                                            # Get body content
                                            body = template.get('body', 'No body')
                                            
                                            # Process the body to extract image references
                                            import re
                                            
                                            # Split the body by image tags to process each part separately
                                            parts = re.split(r'(<img [^>]*>)', body)
                                            
                                            # Body container
                                            st.markdown('<div class="email-body">', unsafe_allow_html=True)
                                            
                                            for part in parts:
                                                # Check if this part is an image tag
                                                img_match = re.match(r'<img src="([^"]+)"([^>]*)>', part)
                                                if img_match:
                                                    # Extract image source
                                                    img_src = img_match.group(1)
                                                    img_attrs = img_match.group(2)
                                                    
                                                    # Handle different image source types
                                                    if img_src.startswith('data:'):
                                                        # Data URL - render as is
                                                        st.markdown(part, unsafe_allow_html=True)
                                                    elif img_src.startswith('data/images/'):
                                                        # Local image - use Streamlit's image component
                                                        try:
                                                            # Full path to the image
                                                            img_path = os.path.join(os.getcwd(), img_src)
                                                            if os.path.exists(img_path):
                                                                # Extract alt text if available
                                                                alt_match = re.search(r'alt="([^"]+)"', part)
                                                                caption = alt_match.group(1) if alt_match else None
                                                                
                                                                # Extract width parameter if available
                                                                width_match = re.search(r'width="([^"]+)"', img_attrs)
                                                                
                                                                # If width is specified and it's a percentage
                                                                if width_match and width_match.group(1).endswith('%'):
                                                                    try:
                                                                        # Convert percentage to float (0-1)
                                                                        width_pct = float(width_match.group(1).replace('%', '')) / 100
                                                                        # Ensure it's within valid range
                                                                        width_pct = max(0.1, min(1.0, width_pct))
                                                                        # Display image with calculated width
                                                                        st.image(img_path, caption=caption, width=int(width_pct * 800))  # Assuming container width is ~800px max
                                                                    except ValueError:
                                                                        # Fallback to container width if conversion fails
                                                                        st.image(img_path, caption=caption, use_container_width=True)
                                                                # If width is specified in pixels or other units
                                                                elif width_match:
                                                                    try:
                                                                        # Try to extract numeric width
                                                                        width_value = width_match.group(1).replace('px', '')
                                                                        width = int(float(width_value))
                                                                        # Display with specified width
                                                                        st.image(img_path, caption=caption, width=width)
                                                                    except ValueError:
                                                                        # Fallback to container width if conversion fails
                                                                        st.image(img_path, caption=caption, use_container_width=True)
                                                                else:
                                                                    # No width specified, use container width
                                                                    st.image(img_path, caption=caption, use_container_width=True)
                                                            else:
                                                                st.warning(f"Image not found: {img_src}")
                                                        except Exception as e:
                                                            st.error(f"Error displaying image: {str(e)}")
                                                    else:
                                                        # External image or other format - render as is
                                                        st.markdown(part, unsafe_allow_html=True)
                                                else:
                                                    # Regular text content - render as is
                                                    if part.strip():
                                                        st.markdown(part, unsafe_allow_html=True)
                                            
                                            # Close body div
                                            st.markdown('</div>', unsafe_allow_html=True)
                                            
                                            # Close preview div
                                            st.markdown('</div>', unsafe_allow_html=True)
                                    
                                    # Close preview button
                                    if st.button("Close Preview", key=f"close_preview_{template_file}_{idx}", type="secondary"):
                                        st.session_state[f"show_preview_{template_file}_{idx}"] = False
                                        st.rerun()
                                
                                # Show image thumbnails if toggled
                                if st.session_state.get(f"show_images_{template_file}_{idx}", False):
                                    st.write("---")
                                    st.write("**Select an image to add to the template:**")
                                    
                                    # Get images from image pool
                                    image_dir = os.path.join('data', 'images')
                                    os.makedirs(image_dir, exist_ok=True)
                                    metadata_file = os.path.join(image_dir, 'metadata.json')
                                    
                                    # Load image metadata
                                    image_metadata = {'images': []}
                                    if os.path.exists(metadata_file):
                                        try:
                                            with open(metadata_file, 'r') as f:
                                                image_metadata = json.load(f)
                                        except Exception as e:
                                            st.error(f"Error loading images: {str(e)}")
                                    
                                    if not image_metadata.get('images', []):
                                        st.info("No images found in the image pool. Please upload images first.")
                                        if st.button("Close", key=f"close_empty_{template_file}_{idx}"):
                                            st.session_state[f"show_images_{template_file}_{idx}"] = False
                                            st.rerun()
                                    else:
                                        # Display images in a 4-column grid
                                        image_cols = st.columns(4)
                                        for i, img in enumerate(image_metadata.get('images', [])):
                                            with image_cols[i % 4]:
                                                # Display thumbnail
                                                if 'thumbnail' in img and img['thumbnail']:
                                                    # Create a unique key for this button using template and image IDs
                                                    btn_key = f"img_{template_file}_{idx}_{img['id']}"
                                                    
                                                    # Display the image name
                                                    img_name = img.get('name') or img.get('original_filename', 'Image')
                                                    st.markdown(f"<div style='text-align:center; font-size:0.8em;'>{img_name}</div>", unsafe_allow_html=True)
                                                    
                                                    # Display the thumbnail with larger size
                                                    st.markdown(f"<div style='text-align:center;'><img src='data:image/png;base64,{img['thumbnail']}' style='max-width:100%; height:auto; transform:scale(1.5);'></div>", unsafe_allow_html=True)
                                                    
                                                    # Add a select button below the image
                                                    if st.button("Select", key=btn_key, use_container_width=True):
                                                        # Get image path
                                                        image_url = f"data/images/{img['filename']}"
                                                        
                                                        # Add image to template body at cursor position if available
                                                        img_tag = f"\n\n<img src=\"{image_url}\" alt=\"{img_name}\" width=\"100%\" />\n\n"
                                                        
                                                        # Get current body text
                                                        body = template.get('body', '')
                                                        cursor_key = f"cursor_pos_{template_file}_{idx}"
                                                        
                                                        # Insert at cursor position if available, otherwise append
                                                        if cursor_key in st.session_state:
                                                            cursor_pos = st.session_state[cursor_key]
                                                            if 0 <= cursor_pos <= len(body):
                                                                # Insert at cursor position
                                                                template['body'] = body[:cursor_pos] + img_tag + body[cursor_pos:]
                                                            else:
                                                                # Fallback to append if cursor position is invalid
                                                                template['body'] = body + img_tag
                                                        else:
                                                            # Fallback to append if no cursor position stored
                                                            template['body'] = body + img_tag
                                                        template_data['template'] = template
                                                        
                                                        # Save updated template
                                                        with open(template_file, 'w') as f:
                                                            json.dump(templates, f, indent=4)
                                                        
                                                        # Hide the thumbnails
                                                        st.session_state[f"show_images_{template_file}_{idx}"] = False
                                                        st.success(f"Image '{img_name}' added to template!")
                                                        st.rerun()
                                        
                                        # Add a close button
                                        if st.button("Close", key=f"close_thumbnails_{template_file}_{idx}"):
                                            st.session_state[f"show_images_{template_file}_{idx}"] = False
                                            st.rerun()
                                
                                # Toggle for metadata
                                if st.toggle('Show Template Details', key=f"show_details_{template_file}_{idx}"):
                                    st.write("---")
                                    detail_col1, detail_col2 = st.columns(2)

                                    with detail_col1:
                                        st.write("**Product Details:**")
                                        st.json(template_data.get('product_data', {}))

                                    with detail_col2:
                                        st.write("**Communication Settings:**")
                                        st.json(template_data.get('settings', {}))

                                        st.write("**Channel Settings:**")
                                        st.write(template_data.get('channels', []))

                                        st.write("**Other Information:**")
                                        st.write(f"- Generated: {template_data.get('generated_at', 'Unknown')}")
                                        st.write(f"- Template Number: {template_data.get('template_number', 'Unknown')}")
                                        st.write(f"- Stage: {template_data.get('stage', 'Unknown')}")

                        # Verification checkbox in second column, outside expander
                        with col2:
                            verified = st.checkbox("",
                                                value=template_data.get('verified', True),
                                                key=f"verify_{template_file}_{idx}")
                            template_data['verified'] = verified

                # Save all changes to file
                with open(template_file, 'w') as f:
                    json.dump(templates, f, indent=4)

            except Exception as e:
                st.error(f"Error loading templates: {str(e)}")

def display_whatsapp_template_addition():
    """Display the WhatsApp Template Addition interface"""
    st.write("## WhatsApp Template Addition")

    # Initialize template manager
    try:
        template_manager = WhatsAppTemplateManager()
    except NameError:
        st.error("WhatsApp template manager module not found. Please make sure the module is installed correctly.")
        return

    # Load products from product_details.json
    products = []
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
    except Exception as e:
        st.error(f"Could not load product details: {str(e)}")
        return

    # Create tabs for different sections
    template_tab, mappings_tab = st.tabs(["Add Template", "Current Mappings"])

    with template_tab:

        # Fetch templates from Gupshup API
        if 'gupshup_templates_df' not in st.session_state:
            try:
                import requests

                url = "https://api.gupshup.io/wa/app/08a81bd9-3865-4db3-8eb2-97d1cf59d153/template"
                headers = {
                    "accept": "application/json",
                    "apikey": "vrn3dzx6eqoejckvtnijjo2iddlfvxhj"
                }

                with st.spinner("Fetching templates from Gupshup..."):
                    try:
                        response = requests.get(url, headers=headers, timeout=10)

                        if response.status_code == 200:
                            try:
                                data = json.loads(response.text)

                                # Check if 'templates' key exists in the response
                                if 'templates' in data and isinstance(data['templates'], list):
                                    templates_list = data['templates']

                                    if templates_list:
                                        # Create DataFrame from templates list
                                        df = pd.DataFrame(templates_list)

                                        # Check if required columns exist
                                        required_columns = ['elementName', 'id', 'createdOn', 'category', 'status']
                                        missing_columns = [col for col in required_columns if col not in df.columns]

                                        if missing_columns:
                                            st.warning(f"Some columns are missing in the API response: {', '.join(missing_columns)}")
                                            # Create empty columns for missing fields
                                            for col in missing_columns:
                                                df[col] = ""

                                        # Convert timestamp to datetime
                                        if 'createdOn' in df.columns:
                                            df['createdTime'] = pd.to_datetime(df['createdOn'], unit='ms', errors='coerce')
                                        else:
                                            df['createdTime'] = pd.NaT

                                        # Rename columns
                                        df.rename(columns={'elementName': 'templateName'}, inplace=True)

                                        # Ensure 'data' column exists
                                        if 'data' not in df.columns:
                                            df['data'] = [{}] * len(df)

                                        # Select only needed columns, handling missing columns gracefully
                                        columns_to_select = ['templateName', 'id', 'data', 'category', 'createdTime', 'status']
                                        available_columns = [col for col in columns_to_select if col in df.columns]

                                        st.session_state.gupshup_templates_df = df[available_columns]
                                        st.session_state.gupshup_templates_raw = templates_list
                                    else:
                                        st.info("No templates found in the Gupshup account.")
                                        st.session_state.gupshup_templates_df = pd.DataFrame()
                                        st.session_state.gupshup_templates_raw = []
                                else:
                                    st.error("Invalid response format: 'templates' key not found or not a list")
                                    st.session_state.gupshup_templates_df = pd.DataFrame()
                                    st.session_state.gupshup_templates_raw = []
                            except json.JSONDecodeError:
                                st.error("Failed to parse API response as JSON")
                                st.session_state.gupshup_templates_df = pd.DataFrame()
                                st.session_state.gupshup_templates_raw = []
                        else:
                            st.error(f"Failed to fetch templates: {response.status_code} - {response.text}")
                            st.session_state.gupshup_templates_df = pd.DataFrame()
                            st.session_state.gupshup_templates_raw = []
                    except requests.exceptions.RequestException as req_err:
                        st.error(f"Request error: {str(req_err)}")
                        st.session_state.gupshup_templates_df = pd.DataFrame()
                        st.session_state.gupshup_templates_raw = []
            except Exception as e:
                st.error(f"Error fetching templates: {str(e)}")
                st.session_state.gupshup_templates_df = pd.DataFrame()
                st.session_state.gupshup_templates_raw = []

        if not products:
            st.warning("No products found in product_details.json. Please check the file.")
        else:
            # Extract product names for dropdown
            product_names = [product.get('Product_Name', '') for product in products]
            selected_product_index = st.selectbox(
                "Select Product",
                range(len(product_names)),
                format_func=lambda i: product_names[i],
                key="map_product"
            )

            # Get selected product details
            selected_product = product_names[selected_product_index]

            # Get current template ID for this product
            current_template_id = template_manager.get_product_template(selected_product)

            # Display templates from Gupshup
            if 'gupshup_templates_df' in st.session_state and not st.session_state.gupshup_templates_df.empty:
                st.write("### Available Templates from Gupshup")

                # Create a filter for template status
                status_filter = st.selectbox(
                    "Filter by Status",
                    ["All", "APPROVED", "PENDING", "REJECTED"],
                    index=0,
                    key="status_filter"
                )

                # Apply filter
                filtered_df = st.session_state.gupshup_templates_df
                if status_filter != "All":
                    filtered_df = filtered_df[filtered_df['status'] == status_filter]

                # Display templates in a table
                st.dataframe(filtered_df[['templateName', 'category', 'status', 'createdTime']])

                # Template selection
                template_options = filtered_df['templateName'].tolist()
                if template_options:
                    selected_template = st.selectbox(
                        "Select Template",
                        template_options,
                        key="selected_template"
                    )

                    # Get template details
                    template_row = filtered_df[filtered_df['templateName'] == selected_template].iloc[0]
                    template_id = template_row['id']

                    # Display template details
                    with st.expander("Template Details", expanded=True):
                        st.write(f"**Template ID:** {template_id}")
                        st.write(f"**Category:** {template_row['category']}")
                        st.write(f"**Status:** {template_row['status']}")
                        st.write(f"**Created:** {template_row['createdTime']}")

                        # Display template data
                        st.write("**Template Data:**")
                        try:
                            # Try to display as JSON if it's a valid JSON object
                            if isinstance(template_row['data'], dict):
                                st.json(template_row['data'])
                            elif isinstance(template_row['data'], str):
                                # If it's a string, display as text
                                st.text(template_row['data'])
                            else:
                                # For any other type, convert to string and display
                                st.text(str(template_row['data']))
                        except Exception as e:
                            # Fallback to displaying as text if JSON conversion fails
                            st.text(str(template_row['data']))

                    # Initialize simplified button data structure
                    button_data = {
                        "type": "None",
                        "buttons": []
                    }

                    # Show current template if exists
                    if current_template_id:
                        st.info(f"Current template ID for {selected_product}: **{current_template_id}**")

                    # Map button
                    button_text = "Add Template Mapping"
                    if st.button(button_text, key="map_button"):
                        # Create a template entry with simplified button data
                        success = template_manager.add_template(
                            template_id=template_id,
                            template_name=selected_template,
                            description=f"Template for {selected_product}",
                            button_data=button_data
                            # Using default values for variable_count and example_text
                        )

                        if success:
                            # Map the template to the product (allowing multiple templates per product)
                            if template_manager.add_product_template_mapping(selected_product, template_id):
                                st.success(f"Template '{selected_template}' mapped to product '{selected_product}' successfully!")
                            else:
                                st.error("Failed to map template to product")
                        else:
                            st.error("Failed to save template")
                else:
                    st.warning("No templates available with the selected filter.")
            else:
                st.warning("No templates found or error fetching templates from Gupshup API.")

                # Fallback to manual template ID input
                st.write("### Manual Template Entry")
                template_id = st.text_input(
                    "Template ID from Gupshup",
                    value=current_template_id if current_template_id else "",
                    help="Enter the template ID provided by Gupshup after template approval",
                    key="template_id_input"
                )

                # Button to refresh templates
                if st.button("Refresh Templates from Gupshup", key="refresh_templates"):
                    # Clear the cached templates to force a refresh
                    if 'gupshup_templates_df' in st.session_state:
                        del st.session_state.gupshup_templates_df
                    if 'gupshup_templates_raw' in st.session_state:
                        del st.session_state.gupshup_templates_raw
                    st.rerun()

    with mappings_tab:
        st.write("### Current Product-Template Mappings")

        # Add a button to test templates
        if st.button("Test Templates", key="test_templates_button"):
            # Set the page to the test message screen
            st.session_state.page = "test_message"
            st.rerun()

        # Get all mappings (multiple templates per product)
        all_mappings = template_manager.get_all_product_template_mappings()

        if not all_mappings:
            st.info("No product-template mappings found.")
        else:
            # Add dropdown to remove templates
            st.write("### Remove Template Using Dropdown")

            # Create a list of options for the dropdown
            dropdown_options = []
            for product, template_ids in all_mappings.items():
                # Find product display name
                product_display_name = product
                for p in products:
                    if p.get('Product_Name') == product:
                        product_display_name = p.get('Product_Name')
                        break

                # Add each template as an option
                for template_id in template_ids:
                    # Get template details
                    template = template_manager.get_template(template_id)
                    template_name = "Unknown"
                    if template:
                        template_name = template.get('template_name', 'Unknown')

                    # Create a display string and store the product and template_id
                    dropdown_options.append({
                        "display": f"{product_display_name} - {template_name} ({template_id})",
                        "product": product,
                        "template_id": template_id,
                        "product_display": product_display_name
                    })

            # Create the dropdown if options exist
            if dropdown_options:
                # Create a list of display strings for the dropdown
                display_options = [option["display"] for option in dropdown_options]

                # Add a "Select a template" option at the beginning
                display_options.insert(0, "Select a template to remove")

                # Create the dropdown
                selected_index = st.selectbox(
                    "Select a template to remove:",
                    range(len(display_options)),
                    format_func=lambda i: display_options[i],
                    key="remove_template_dropdown"
                )

                # If a template is selected (not the first option)
                if selected_index > 0:
                    # Get the selected template details
                    selected_option = dropdown_options[selected_index - 1]
                    product = selected_option["product"]
                    template_id = selected_option["template_id"]
                    product_display = selected_option["product_display"]

                    # Add a remove button
                    if st.button("Remove Selected Template", key="remove_selected_template"):
                        # Remove the template from the product mappings
                        if product in all_mappings and template_id in all_mappings[product]:
                            all_mappings[product].remove(template_id)

                            # If this was the last template for the product, remove the product entry
                            if not all_mappings[product]:
                                del all_mappings[product]

                            # Update the mappings in the template manager
                            template_manager.templates["product_template_mappings"] = all_mappings

                            # Save the changes
                            if template_manager._save_templates():
                                st.success(f"Template {template_id} removed from {product_display}")
                                st.rerun()
                            else:
                                st.error("Failed to save changes")

            st.divider()

            # Display each mapping with edit options
            st.write("### Individual Template Listings:")

            for product, template_ids in all_mappings.items():
                # Find product in product_details.json if possible
                product_display_name = product
                for p in products:
                    if p.get('Product_Name') == product:
                        product_display_name = p.get('Product_Name')
                        break

                # Create a section for each product
                with st.expander(f"**{product_display_name}** ({len(template_ids)} templates)"):
                    # Create a table for this product's templates
                    for i, template_id in enumerate(template_ids):
                        # Get template details
                        template = template_manager.get_template(template_id)

                        # Create columns for each row
                        col1, col2, col3 = st.columns([3, 7, 1])

                        with col1:
                            st.write(f"**Template ID:** {template_id}")
                            if template:
                                st.write(f"### Template {template.get('template_number', i+1)}")
                            st.write(f"**Generated at:** {template.get('generated_at', 'Unknown')}")
                            
                            # Display template tags with custom styling
                            tags = template.get('tags', [])
                            # Filter out None values and empty strings from tags
                            tags = [tag for tag in tags if tag and tag != 'None']
                            if tags:
                                st.write("**Tags:**")
                                tags_html = ""
                                # Use brand color for all tags
                                brand_color = "#02C688"  # Brand color
                                
                                # Define emoji mappings for different tag types
                                # Feature-related emojis
                                feature_keywords = {
                                    "report": "📊", "dashboard": "📈", "chart": "📉", "analytics": "📊", "data": "📊",
                                    "ai": "🤖", "ml": "🤖", "intelligent": "🤖", "smart": "🤖", "automation": "⚙️", "auto": "⚙️", 
                                    "security": "🔒", "secure": "🔒", "protection": "🛡️", "privacy": "🔐",
                                    "chat": "💬", "message": "💬", "notification": "🔔", "alert": "🚨",
                                    "search": "🔍", "find": "🔍", "discover": "🔍",
                                    "cloud": "☁️", "storage": "🗄️", "backup": "💾",
                                    "performance": "⚡", "speed": "⚡", "fast": "⚡",
                                    "mobile": "📱", "responsive": "📱", "app": "📱",
                                    "video": "🎥", "audio": "🎵", "music": "🎵",
                                    "social": "👥", "community": "👥", "network": "🌐",
                                    "integration": "🔄", "connect": "🔌", "sync": "🔄",
                                    "payment": "💳", "subscription": "💲", "billing": "💵",
                                    "product": "📦", "item": "📦", "goods": "📦"
                                }
                                
                                # Offer-related emojis
                                offer_keywords = {
                                    "free": "🆓", "trial": "🆓", "discount": "💰", "sale": "🏷️", "deal": "🏷️",
                                    "limited": "⏱️", "exclusive": "🔑", "special": "✨", "vip": "🌟",
                                    "new": "🆕", "upgrade": "⬆️", "premium": "👑", "pro": "⭐",
                                    "bonus": "🎁", "gift": "🎁", "extra": "➕", "plus": "➕",
                                    "early": "🕐", "access": "🔓", "invite": "📨"
                                }
                                
                                # Motivation-related emojis
                                motivation_keywords = {
                                    "time": "⏰", "save": "💾", "quick": "⚡", "efficient": "⚡",
                                    "easy": "👌", "simple": "👌", "hassle": "👌", "convenient": "👌",
                                    "growth": "📈", "increase": "📈", "boost": "🚀", "improve": "📈",
                                    "success": "🏆", "achieve": "🏆", "win": "🏆", "goal": "🎯",
                                    "productivity": "📈", "effective": "✅", "efficient": "⚡",
                                    "solution": "💡", "solve": "🔧", "fix": "🔧",
                                    "roi": "💰", "revenue": "💰", "profit": "💰", "money": "💰",
                                    "cost": "💲", "budget": "💲", "affordable": "💲",
                                    "transform": "✨", "revolutionize": "💫", "innovate": "💡",
                                    "support": "🤝", "help": "🤝", "assist": "🤝"
                                }
                                
                                # Default emojis based on position (in case no keyword match is found)
                                default_emojis = ["⚙️", "🏷️", "🚀", "📌"]
                                
                                for i, tag in enumerate(tags):
                                    # Use brand color for all tags
                                    bg_color = brand_color
                                    # Set text color to white for better contrast on brand color
                                    text_color = "#ffffff"
                                    # Add subtle shadow effect for depth
                                    shadow = "box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);"
                                    # Make tag more prominent with hover effect
                                    hover = "transition: all 0.3s cubic-bezier(.25,.8,.25,1);"
                                    
                                    # Find an appropriate emoji for the tag
                                    emoji = ""
                                    tag_lower = tag.lower()
                                    
                                    # Check for feature keywords
                                    if i == 0 or any(keyword in tag_lower for keyword in feature_keywords):
                                        for keyword, kw_emoji in feature_keywords.items():
                                            if keyword in tag_lower:
                                                emoji = kw_emoji
                                                break
                                        if not emoji:
                                            emoji = default_emojis[0]  # Default feature emoji
                                    # Check for offer keywords
                                    elif i == 1 or any(keyword in tag_lower for keyword in offer_keywords):
                                        for keyword, kw_emoji in offer_keywords.items():
                                            if keyword in tag_lower:
                                                emoji = kw_emoji
                                                break
                                        if not emoji:
                                            emoji = default_emojis[1]  # Default offer emoji
                                    # Check for motivation keywords
                                    elif i == 2 or any(keyword in tag_lower for keyword in motivation_keywords):
                                        for keyword, kw_emoji in motivation_keywords.items():
                                            if keyword in tag_lower:
                                                emoji = kw_emoji
                                                break
                                        if not emoji:
                                            emoji = default_emojis[2]  # Default motivation emoji
                                    else:
                                        emoji = default_emojis[3]  # Default generic emoji
                                    
                                    # Add emoji to tag
                                    tag_with_emoji = f"{emoji} {tag}"
                                    
                                    tags_html += f"<span style='display:inline-block; background-color:{bg_color}; color:{text_color}; padding:5px 12px; margin:3px 6px 3px 0; border-radius:20px; font-size:0.9em; font-weight:500; {shadow} {hover}'>{tag_with_emoji}</span>"
                                st.markdown(tags_html, unsafe_allow_html=True)
                            
                            # Indicate if template includes emojis (with specific details)
                            has_emojis_subject = template.get('has_emojis_subject', False)
                            has_emojis_body = template.get('has_emojis_body', False)
                            
                            # Use fallback for backward compatibility
                            if 'has_emojis_subject' not in template and 'has_emojis' in template:
                                has_emojis = template.get('has_emojis', False)
                                has_emojis_subject = has_emojis
                                has_emojis_body = has_emojis
                            
                            if has_emojis_subject and has_emojis_body:
                                st.markdown("<span style='color:#09ab3b; font-size:0.9em;'>✓ Emojis in subject & body</span>", unsafe_allow_html=True)
                            elif has_emojis_subject:
                                st.markdown("<span style='color:#09ab3b; font-size:0.9em;'>✓ Emojis in subject</span>", unsafe_allow_html=True)
                            elif has_emojis_body:
                                st.markdown("<span style='color:#09ab3b; font-size:0.9em;'>✓ Emojis in body</span>", unsafe_allow_html=True)

                            template_data = template.get('template', {})
                            subject = template_data.get('subject', '')
                            body = template_data.get('body', '')

                        with col2:
                            if template and "example_text" in template:
                                st.write("**Template Preview:**")
                                st.text(template.get('example_text', ''))

                        with col3:
                            # Add delete button
                            if st.button("🗑️", key=f"delete_{product}_{template_id}"):
                                # Remove the template from the product mappings
                                if product in all_mappings and template_id in all_mappings[product]:
                                    all_mappings[product].remove(template_id)

                                    # If this was the last template for the product, remove the product entry
                                    if not all_mappings[product]:
                                        del all_mappings[product]

                                    # Update the mappings in the template manager
                                    template_manager.templates["product_template_mappings"] = all_mappings

                                    # Save the changes
                                    if template_manager._save_templates():
                                        st.success(f"Template {template_id} removed from {product_display_name}")
                                        st.rerun()
                                    else:
                                        st.error("Failed to save changes")

                        # Add a separator between templates
                        st.markdown("---")

            # Also show a consolidated table view
            st.write("### All Mappings")
            mapping_data = []

            for product, template_ids in all_mappings.items():
                # Find product display name
                product_display_name = product
                for p in products:
                    if p.get('Product_Name') == product:
                        product_display_name = p.get('Product_Name')
                        break

                # Add each template mapping as a row
                for template_id in template_ids:
                    # Get template details
                    template = template_manager.get_template(template_id)
                    template_name = "Unknown"

                    if template:
                        template_name = template.get('template_name', 'Unknown')

                    mapping_data.append({
                        "Product": product_display_name,
                        "Template ID": template_id,
                        "Template Name": template_name
                    })

            # Display as table
            if mapping_data:
                st.table(pd.DataFrame(mapping_data))
            else:
                st.info("No mappings available after deletions.")

            # Test Templates button removed as requested

def get_stage_template(stage, product_name, organization_url=None):
    """Get template for a specific stage and product

    Args:
        stage: The journey stage
        product_name: The product name to match
        organization_url: Optional organization URL to filter by
    """
    template_file = f'data/templates/{stage.lower().replace(" ", "_")}.json'
    if os.path.exists(template_file):
        with open(template_file, 'r') as f:
            templates = json.load(f)

            # Filter by product name and optionally by organization URL
            matching_templates = []
            for t in templates:
                product_data = t.get('product_data', {})
                if product_data.get('Product_Name') == product_name:
                    # If organization URL is provided, check if it matches
                    if organization_url is None or product_data.get('Company_URL', '') == organization_url:
                        matching_templates.append(t)

            if matching_templates:
                return matching_templates[-1]  # Use most recent template
    return None
