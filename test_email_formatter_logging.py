#!/usr/bin/env python3
"""
Test script to verify email formatter logging functionality.
This script will test the email formatter module and generate logs.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, 'src')

def test_email_formatter_logging():
    """Test the email formatter logging functionality."""
    print("Testing Email Formatter Logging...")
    
    try:
        # Import the email formatter module (this will initialize logging)
        from openengage.core.email_formatter import text_to_html, logger
        
        print("✓ Email formatter module imported successfully")
        print("✓ Logger initialized")
        
        # Test basic email conversion
        email_content = {
            'subject': 'Test Email',
            'content': '''Hi there,

This is a test email to verify that logging is working properly.

Our platform offers amazing features. You can get started by visiting our website.

Best regards,
Test Team'''
        }
        
        # Test with various parameters to generate different log entries
        print("\n--- Testing email conversion with logging ---")
        
        # Test 1: Basic conversion
        logger.info("Starting test 1: Basic email conversion")
        result1 = text_to_html(email_content)
        print(f"✓ Test 1 completed - Generated {len(result1)} characters")
        
        # Test 2: With product URL and name
        logger.info("Starting test 2: Email conversion with product details")
        result2 = text_to_html(
            email_content=email_content,
            product_url='https://example.com/product',
            product_name='TestProduct'
        )
        print(f"✓ Test 2 completed - Generated {len(result2)} characters")
        
        # Test 3: With communication settings
        logger.info("Starting test 3: Email conversion with communication settings")
        communication_settings = {
            'utm_source': 'email',
            'utm_medium': 'test',
            'utm_campaign': 'logging_test',
            'sender_name': 'Test Sender'
        }
        result3 = text_to_html(
            email_content=email_content,
            product_url='https://example.com/dashboard',
            product_name='TestPlatform',
            communication_settings=communication_settings,
            recipient_email='<EMAIL>',
            recipient_first_name='John'
        )
        print(f"✓ Test 3 completed - Generated {len(result3)} characters")
        
        # Test 4: Error scenarios
        logger.info("Starting test 4: Testing error scenarios")
        
        # Test with invalid input
        result4 = text_to_html(None)
        print(f"✓ Test 4a completed - Handled None input")
        
        result5 = text_to_html({})
        print(f"✓ Test 4b completed - Handled empty dict input")
        
        result6 = text_to_html({'content': ''})
        print(f"✓ Test 4c completed - Handled empty content")
        
        logger.info("All tests completed successfully")
        print("\n✓ All tests completed successfully!")
        
        # Check if log file was created
        log_files = [f for f in os.listdir('data/logs') if f.startswith('email_formatter_') and f.endswith('.log')]
        if log_files:
            latest_log = max(log_files)
            log_path = os.path.join('data/logs', latest_log)
            log_size = os.path.getsize(log_path)
            print(f"✓ Log file created: {latest_log} ({log_size} bytes)")
            
            # Show a few lines from the log file
            print(f"\n--- Sample log entries from {latest_log} ---")
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[-10:], 1):  # Show last 10 lines
                    print(f"{len(lines)-10+i:2d}: {line.strip()}")
        else:
            print("⚠ No log file found - there might be an issue with logging setup")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Make sure you're running this script from the project root directory")
        return False
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("Email Formatter Logging Test")
    print("=" * 50)
    
    success = test_email_formatter_logging()
    
    print("\n" + "=" * 50)
    if success:
        print("✓ Logging test completed successfully!")
        print("Check the data/logs directory for the generated log file.")
    else:
        print("✗ Logging test failed!")
    print("=" * 50)

if __name__ == "__main__":
    main()
