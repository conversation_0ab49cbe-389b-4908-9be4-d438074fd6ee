#!/usr/bin/env python3
"""
Test script for email formatter logging functionality.

This script tests the email formatter module to generate logs and verify
that the logging system is working correctly.
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, 'src')

try:
    from openengage.core.email_formatter import (
        text_to_html,
        load_template_cta,
        get_cta_text_for_template,
        create_cta_button,
        load_brand_guidelines
    )
    print("Successfully imported email formatter modules")
except ImportError as e:
    print(f"Error importing email formatter modules: {e}")
    print("Make sure you're running this script from the project root directory")
    sys.exit(1)

def test_basic_email_conversion():
    """Test basic email conversion functionality."""
    print("\n" + "="*50)
    print("Testing Basic Email Conversion")
    print("="*50)
    
    # Sample email content
    email_content = {
        'subject': 'Welcome to Our Platform',
        'content': '''Hi there,

Welcome to our amazing platform! We're excited to have you on board.

Our platform offers cutting-edge features that will revolutionize your workflow. You can get started immediately by exploring our dashboard.

We've prepared some helpful resources to get you started. Check out our getting started guide and tutorials.

Best regards,
The Platform Team'''
    }
    
    # Sample communication settings
    communication_settings = {
        'utm_source': 'email',
        'utm_medium': 'welcome_email',
        'utm_campaign': 'user_onboarding',
        'utm_content': 'welcome_message',
        'sender_name': 'Platform Team',
        'organization_url': 'https://www.example.com'
    }
    
    # Convert to HTML
    html_result = text_to_html(
        email_content=email_content,
        product_url='https://www.example.com/dashboard',
        product_name='Platform',
        communication_settings=communication_settings,
        recipient_email='<EMAIL>',
        recipient_first_name='John',
        template_name='welcome_email'
    )
    
    print(f"Conversion successful: {len(html_result)} characters generated")
    return html_result

def test_cta_functionality():
    """Test CTA loading and generation functionality."""
    print("\n" + "="*50)
    print("Testing CTA Functionality")
    print("="*50)
    
    # Test CTA loading for different templates
    test_templates = [
        'welcome_email',
        'product_launch',
        'newsletter',
        'nonexistent_template'
    ]
    
    for template in test_templates:
        print(f"\nTesting template: {template}")
        cta_text = load_template_cta(template)
        print(f"  Direct load result: {cta_text}")
        
        final_cta = get_cta_text_for_template(template, 'Default CTA')
        print(f"  Final CTA text: {final_cta}")

def test_brand_guidelines():
    """Test brand guidelines loading."""
    print("\n" + "="*50)
    print("Testing Brand Guidelines")
    print("="*50)
    
    # Test with different organization URLs
    test_urls = [
        'https://www.example.com',
        'https://www.analyticsvidhya.com',
        'https://nonexistent.com',
        None
    ]
    
    for url in test_urls:
        print(f"\nTesting URL: {url}")
        guidelines = load_brand_guidelines(url)
        print(f"  Guidelines loaded: {type(guidelines)} with {len(guidelines) if isinstance(guidelines, dict) else 'N/A'} keys")

def test_cta_button_creation():
    """Test CTA button creation with different configurations."""
    print("\n" + "="*50)
    print("Testing CTA Button Creation")
    print("="*50)
    
    # Sample brand guidelines
    brand_guidelines = {
        'primary_color': '#2674ED',
        'secondary_color': '#4A90E2',
        'button_style': 'Rounded',
        'border_radius': '8px',
        'cta_size': 'Medium',
        'font': 'Arial'
    }
    
    # Test different CTA configurations
    test_configs = [
        ('Get Started', 'https://example.com/start'),
        ('Learn More', 'https://example.com/learn'),
        ('Download Now', 'https://example.com/download')
    ]
    
    for cta_text, url in test_configs:
        print(f"\nCreating CTA: {cta_text}")
        button_html = create_cta_button(cta_text, url, brand_guidelines)
        print(f"  Button HTML length: {len(button_html)} characters")

def test_error_scenarios():
    """Test error handling scenarios."""
    print("\n" + "="*50)
    print("Testing Error Scenarios")
    print("="*50)
    
    # Test with invalid inputs
    print("Testing with None email content...")
    result = text_to_html(None)
    print(f"  Result: {result}")
    
    print("Testing with empty email content...")
    result = text_to_html({})
    print(f"  Result: {result}")
    
    print("Testing with malformed email content...")
    result = text_to_html({'subject': 'Test', 'content': ''})
    print(f"  Result: {result}")

def main():
    """Main test function."""
    print("Email Formatter Logging Test")
    print("This script will test various email formatter functions to generate logs.")
    
    try:
        # Run all tests
        test_basic_email_conversion()
        test_cta_functionality()
        test_brand_guidelines()
        test_cta_button_creation()
        test_error_scenarios()
        
        print("\n" + "="*50)
        print("All tests completed successfully!")
        print("Check the data/logs directory for generated log files.")
        print("="*50)
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
