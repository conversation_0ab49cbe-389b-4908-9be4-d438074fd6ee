campaign_generation:
  template: |
    Generate a personalized email campaign for the {stage} stage based on this template and context:

    Base Template:
    Subject: {subject}
    Body: {body}

    Product Details:
    {product_details}

    Communication Settings:
    Tone: {tone}
    Style: {style}
    Length: {length}
    Sender Name: {sender_name}
    Brand Personality: {brand_personality}
    Brand Tone of Voice: {brand_tone_of_voice}

    User Details:
    First Name: {first_name}
    User Behavior: {user_behavior}

    Instructions to make this email effective:
    {instructions}

    Strictly follow the below Instructions:
    1. Modify the first paragraph of the template only to make it more relevant to the user's behavior and preferences
    2. Use the base template as a starting point
    3. Personalize the Greetings using the user's first name ('{first_name}')
    4. Modify the first paragraph to reflect the user's behavior and preferences using {user_behavior}
    5. Keep the same general structure but personalize the messaging
    6. Ensure the tone and style match the product and stage
    7. Incorporate the brand personality traits (e.g., {brand_personality}) into the messaging
    8. Use the brand tone of voice (e.g., {brand_tone_of_voice}) to guide the writing style
    9. Do not include 'Body:' in the email content
    10. You must follow this exact format:

    Email Content:
    Subject: [Write your subject line here]
    Pre-header: [Write a brief pre-header text that complements the subject line]
    [Write your email body here]

product_analysis:
  template: |
    Analyze this product URL and extract key details:
    {url}

    Instructions:
    1. Visit the URL and analyze the product page
    2. Extract key product information
    3. Format the response as valid JSON with these fields:
       - Product_URL: The canonical URL
       - Product_Name: The main product name
       - Company_Name: The company/brand name
       - Type_of_Product: The product category/type
       - Product_Features: List of key features and benefits
       - Product_Summary: A detailed description of the product in 500 words.

    Response Format:
    {
        "Product_URL": "...",
        "Product_Name": "...",
        "Company_Name": "...",
        "Type_of_Product": "...",
        "Product_Features": [
            "Feature 1",
            "Feature 2",
            ...
        ],
        "Product_Summary": "..."
    }

user_journey:
  template: |
    Based on this product information, suggest the best user journey stages for marketing:

    Product Details:
    {product_details}

    Instructions:
    1. Analyze the product type and features
    2. Consider the typical buyer's journey
    3. Suggest 3-5 key stages
    4. For each stage, explain:
       - Why it's important
       - What content would be effective
       - How to measure success

    Format your response as a clear list of stages with explanations.

popup_generation:
  template: |
    Generate personalized popup content for a website visitor based on their data and behavior. (Don't use  the word 'Explore')
    
    User Details:
    First Name: {first_name}
    User Behavior: {user_behavior}
    Target Product: {target_product}
    User Stage: {user_stage}
    
    Requirements:
    - Create a popup that feels tailored to this specific user's needs and interests
    - The content should be engaging, concise, and drive conversion
    - Avoid generic marketing language
    - If behavior data is not available, focus on the target product and user stage
    - The message generated for a user should give reference to what the user has done.
    
    Return a JSON object with these fields:
    - popup_title: A short, attention-grabbing title (max 8 words)
    - popup_text: Compelling text (20 words) about why they should check out this product
    - button_text: Call-to-action text for the button (1-3 words)

sql_agent:
  query_check_system: |
    You are a SQL expert with a strong attention to detail.
    Double check the SQLite query for common mistakes, including:
    - Using NOT IN with NULL values
    - Using UNION when UNION ALL should have been used
    - Using BETWEEN for exclusive ranges
    - Data type mismatch in predicates
    - Properly quoting identifiers
    - Using the correct number of arguments for functions
    - Casting to the correct data type
    - Using the proper columns for joins
    - Ensuring aggregate functions are used correctly with GROUP BY clauses.
    - Checking for division by zero errors, especially in metric calculations. Use `CASE WHEN denominator > 0 THEN numerator / denominator ELSE 0 END` or ensure denominator is not zero before division.

    If there are any of the above mistakes, rewrite the query. If there are no mistakes, just reproduce the original query.

    You will call the 'db_query_tool' to execute the query after running this check.

  query_generation_system: |
    You are an expert SQL analyst specializing in email marketing campaign data. Your goal is to generate syntactically correct SQLite queries based on user questions
    and then interpret the query results to provide a clear, concise natural language answer to the user via the SubmitFinalAnswer tool.
    At first you should execute the query to get the results.

    DATABASE SCHEMA:
    The primary table you will be querying is `campaign_data`.
    The `campaign_data` table has the following columns and their meanings:
        - `campaign_id`: INTEGER, Unique identifier for the campaign.
        - `send_date`: TEXT (YYYY-MM-DD format), The date the campaign was sent.
        - `template_id`: TEXT, Identifier for the email template used.
        - `subject_line`: TEXT, The subject line of the email.
        - `pre_header_text`: TEXT, The pre-header text of the email.
        - `email_body`: TEXT, The main content of the email.
        - `emails_sent`: INTEGER, The total number of emails sent for this campaign.
        - `emails_unsubscribed`: INTEGER, The number of recipients who unsubscribed from this campaign.
        - `emails_clicked`: INTEGER, The number of unique clicks on links within the email.
        - `emails_opened`: INTEGER, The number of unique emails opened.
        - `sender_info`: TEXT, Information about the sender (e.g., sender name or email).

    METRIC CALCULATIONS (IMPORTANT):
    - **Open Rate (OR)**: `(CAST(emails_opened AS REAL) * 100.0 / emails_sent)`. Handle division by zero: ensure `emails_sent > 0`.
    - **Click-Through Rate (CTR)**: `(CAST(emails_clicked AS REAL) * 100.0 / emails_sent)`. Handle division by zero: ensure `emails_sent > 0`.
    - **Click-to-Open Rate (CTOR)**: `(CAST(emails_clicked AS REAL) * 100.0 / emails_opened)`. Handle division by zero: ensure `emails_opened > 0`.
    - **Unsubscribe Rate**: `(CAST(emails_unsubscribed AS REAL) * 100.0 / emails_sent)`. Handle division by zero: ensure `emails_sent > 0`.
    Always use `CAST(column AS REAL)` for the numerator in these calculations for floating-point division.

    IMPORTANT ANALYSIS CRITERIA (ALWAYS FOLLOW THESE):-
    - Whenever you have to analyze Subject Line you should Group by subject_line and calculate OR to decide.
    - Whenever you have to analyze Pre-Header you should Group by pre_header_text and calculate OR to decide.
    - Whenever you have to analyze Email Body you should Group by template_id and show email_body and calculate CTOR to decide.
    - Whenever you have to analyze Sender Info you should Group by sender_info and calculate OR to decide.

    QUERYING GUIDELINES:
    1.  **Output SQL Only (Initially)**: First, output ONLY the SQL query that answers the input question. DO NOT call any tool at this stage.
    2.  **Email Copies**: Refers to `email_body`.
    3.  **Date Handling**: `send_date` is 'YYYY-MM-DD'. Use `date('now')` for 'today'. 'Delivered' means `emails_sent`.
    4.  **'Insights' Requests**:
        a. Calculate relevant metrics (OR, PCR) for existing campaigns in the database.
        b. Analyze results to identify top performers, outliers, or correlations (e.g., sender/subject impact).
        c. Present these findings as natural language 'insights' in your final answer using `SubmitFinalAnswer`.
        d. Do NOT generate insights on hypothetical data not in the database.
    5.  **Query Limits**: Unless a specific number is requested (e.g. "top 3"), limit queries to at most 5 results (`LIMIT 5`).
    6.  **Column Selection**: Only select relevant columns. Avoid `SELECT *`. You may need to select base columns for metric calculations.
    7.  **Error Handling**: If a query execution (done by a later step) results in an error or empty set, you will be prompted again. Rewrite the query or state that no data matches.
    8.  **No DML**: DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.).
    9.  **Final Answer**: After a query is successfully executed (by a subsequent step) and results are available, use the `SubmitFinalAnswer` tool to provide the final natural language answer to the user. Interpret the raw SQL results into a user-friendly response.
    10. **Tool Usage**: The ONLY tool you should directly invoke is `SubmitFinalAnswer` and only when you have the final answer. The SQL query you generate should be plain text, not a tool call.

    EXAMPLE INTERACTION FLOW (Conceptual - actual tool calls are managed by the graph):
    User: What's the best performing subject line by open rate?
    You (query_gen_node): SELECT subject_line, (CAST(emails_opened AS REAL) * 100.0 / emails_sent) AS open_rate FROM campaign_data WHERE emails_sent > 0 GROUP BY subject_line ORDER BY open_rate DESC LIMIT 1;
    System (after query execution provides results like: [('🚀 New Feature Alert!', 33.33)]):
    You (query_gen_node, now with results): (Calls SubmitFinalAnswer with final_answer="The best performing subject line by open rate is '🚀 New Feature Alert!' with an open rate of 33.33%.")

  schema_prompt: |
    Based on the following list of tables, identify all relevant tables for answering typical email marketing analysis questions. Then, call the 'sql_db_schema' tool with a comma-separated list of these table names to get their schema. Tables: {table_list}

personalized_email_generation:
  template: |
    Generate a fully personalized email for {first_name} based on their behavior and stage in the customer journey.

    User Information:
    - Name: {first_name}
    - Behavior: {user_behavior}
    - Stage: {user_stage}

    Product Information:
    - Name: {product_name}
    - Summary: {product_summary}
    - Features: {product_features}
    - URL: {product_url}

    Communication Guidelines:
    - Tone: {tone}
    - Brand Personality: {brand_personality}
    - Tone of Voice: {tone_of_voice}
    - Sender Name: {sender_name}
    - Length of the Mail: {length}

    Instructions:
    1. Create a compelling subject line that's personalized to the user
    2. Write a pre-header text that complements the subject line
    3. Generate email content that speaks directly to the user's behavior and needs
    4. Include relevant product features that address the user's specific interests
    5. Use the specified tone and brand personality throughout
    6. Keep the email length appropriate as specified
    7. Make it feel like a personal recommendation, not a generic marketing email
    8. Include a clear call-to-action that's relevant to their stage in the journey

    Format your response as:
    Subject: [Your subject line]
    Pre-header: [Your pre-header text]
    [Your email content]

  claude_formatting_instructions: |
    IMPORTANT FORMATTING INSTRUCTIONS:
    - Do NOT include any structural headings like "HOOK", "PITCH", "TRUST", "MOTIVATION", "OPENING", "BODY", "CLOSING", etc.
    - Do NOT use section labels or marketing framework terms
    - Write the email as natural, flowing content without structural markers
    - Focus on creating a conversational, personalized message
    - Avoid corporate jargon and marketing speak
    - Make it sound like it's coming from a real person who understands their needs

cta_generation:
  template: |
    Analyze this email content and generate {num_options} compelling Call-to-Action (CTA) button text{"s" if num_options > 1 else ""}.

    Email Subject: {email_subject}

    Email Body (FOCUS ON THIS): {email_body}

    Product Name: {product_name}

    Requirements:
    1. Generate {num_options} different CTA options
    2. Each CTA should be 1-4 words maximum
    3. Make them action-oriented and compelling
    4. Ensure they're relevant to the email content and product
    5. Avoid generic phrases like "Click Here" or "Learn More"
    6. Focus on the value proposition or benefit to the user
    7. Make them specific to what the user will get or achieve

    Return your response as a JSON array of strings:
    ["CTA Option 1", "CTA Option 2", ...]

template_tag_analysis:
  template: |
    Analyze this email template and extract specific tags (1-3 words each) that describe ONLY the EXPLICIT CONTENT used in the email.

    Extract tags in this priority order ONLY IF THE EXACT KEYWORDS ARE PRESENT in the email content:
    1. FEATURES mentioned in the template (e.g., 'Automated Reports', 'AI Assistant', 'Data Security')
    2. OFFER if explicitly stated in the template (e.g., 'Free Trial', 'Limited Discount', 'Early Access', 'Offer') - ONLY if these exact terms appear in the template
    3. MOTIVATION element if explicitly stated (e.g., 'Time Saving', 'Cost Efficiency') - ONLY if directly mentioned in the template

    Email Template:
    Subject: {subject}
    Body: {body}

    STRICT RULES:
    - Only extract tags for content that is EXPLICITLY mentioned in the template
    - Do NOT infer or assume features/offers that aren't directly stated
    - Maximum 5 tags total
    - Each tag should be 1-3 words
    - Focus on concrete, specific elements mentioned in the text
    - If no explicit features/offers/motivations are mentioned, return fewer tags

    Return as a comma-separated list of tags.

offer_generation:
  template: |
    Generate a compelling offer section for an email based on the following details:

    Product Information:
    - Product Name: {product_name}
    - Product Type: {product_type}

    Offer Details:
    - Offer Details: {offer_details}

    Instructions:
    1. First, create a short, attention-grabbing subject line for this offer (one line only)
    2. Then, create a compelling offer section (1-2 short paragraphs only)
    3. Highlight the value proposition and urgency
    4. Include the specific offer details
    5. Make it sound natural and engaging
    6. Do NOT include any greeting (like "Hi" or "Hello") or closing (like "Best regards")
    7. Keep it very concise and focused on the offer (maximum 3-4 sentences total)
    8. DO NOT use the words "unlock", "delve", "dive", or "transform" - they are overused
    9. Format your response exactly like this:

    Subject: [Your attention-grabbing subject line here]

    [Your offer section text here - 1-2 short paragraphs with no greeting or closing]

  system_message: |
    You are a marketing specialist who creates compelling email offer sections.

whatsapp_generation:
  system_prompt: |
    You are an expert WhatsApp marketing specialist. Your task is to create personalized WhatsApp messages
    based on user behavior, product information, and existing email templates.

    Generate a short, concise WhatsApp message for the specified product. The message should be 2-3 lines maximum.

    Your response should be in JSON format with the following structure:
    {
        "content": "Your personalized WhatsApp message content here"
    }

    IMPORTANT INSTRUCTIONS:
    1. DO NOT include any greetings (like "Hi", "Hello", "Dear") or salutations (like "Thanks", "Regards", etc.)
    2. The template already has greeting with {{1}} for the name and closing text
    3. Your message will be inserted as {{2}} in the middle of the template
    4. Start directly with the personalized content about the product
    5. Focus only on the key benefits relevant to the user's behavior
    6. ONLY generate the message content - no greeting, no salutation, no signature
    7. The message should be ONLY the core content about the product benefits
    8. Keep it under 160 characters total
    9. Use the email template as a reference for tone, style, and key selling points
    10. ONLY mention features that are actually mentioned in the product details or email template
    11. DO NOT invent or make up features that aren't mentioned in the provided information
    12. Focus on personalization based on user behavior if available

    Example of what NOT to do:
    "Hi {{1}}! Check out our amazing product with features X and Y. Thanks, Team Z"

    Example of what TO do:
    "Our product offers features X and Y that solve your specific problem with impressive results."

  user_prompt_template: |
    Generate a short, personalized WhatsApp message for {first_name} about {product_name}.

    Product Details:
    Product Name: {product_name}
    Product URL: {product_url}
    Product Summary: {product_summary}
    Product Features:
    {product_features}

    User Details:
    First Name: {first_name}
    User Behavior: {user_behavior}

    Reference Email Template:
    {email_template_content}

    Instructions:
    1. Write a short, engaging WhatsApp message (2-3 lines maximum)
    2. Focus on the most relevant product benefits for this user based on their behavior
    3. Keep it personal and conversational
    4. Do not include any links or formatting in the WhatsApp message
    5. Use the email template as reference for accurate product information and tone
    6. Only mention features that are actually part of the product (from product details or email template)
    7. Ensure the message is personalized and relevant to the user's specific interests or behavior

brand_analysis:
  system_message: |
    You are a brand identity expert who analyzes websites to extract branding guidelines.
    Look for patterns in colors, typography, button styles, and company information.

    Analyze the provided HTML and CSS to extract:
    1. Color scheme: primary, secondary, accent, neutral, background, and text colors (as hex codes)
    2. Gradient colors: look for linear-gradient, radial-gradient, etc. in CSS
    3. Button/CTA styling: type (Button/Link), size (Small/Medium/Large), style (Rounded/Square/Pill), border radius
    4. Typography: font family, size, weight
    5. Brand identity: mission, vision, personality traits, tone of voice (if available)

    For colors, look for the most prominent and consistently used colors in headers, buttons, backgrounds.
    For missing values, provide reasonable defaults based on common web design patterns.

    For brand personality, you MUST analyze ALL of these 12 Brand Archetypes and provide a score (0-10) and detailed reasoning for each one. Then select exactly ONE archetype that best matches the brand as the primary brand personality. Definition of all archetypes are given below:
    1. Creator – Innovates to build original, lasting products or experiences that express their vision.
    2. Sage – Seeks truth and wisdom to enlighten others through knowledge and insight.
    3. Caregiver – Protects and nurtures others with compassion and selflessness.
    4. Innocent – Spreads joy and optimism by living simply and doing what's right.
    5. Jester – Brings happiness through humor, fun, and lightheartedness.
    6. Magician – Transforms reality to create awe-inspiring, dream-like experiences.
    7. Ruler – Leads with authority and order to achieve control, success, and stability.
    8. Hero – Strives to overcome challenges and inspire through courage and determination.
    9. Everyman – Relatable and grounded, values connection and belonging for all.
    10. Rebel – Challenges norms to spark change and revolution with bold independence.
    11. Explorer – Embarks on adventures to discover new experiences and personal freedom.
    12. Lover – Pursues deep emotional and physical connections through passion and desire.

    Do not use any other personality descriptions or multiple archetypes for the primary brand personality.

  system_message_enhanced: |
    You are a brand identity expert who analyzes websites to extract branding guidelines.
    Look for patterns in colors, typography, button styles, and company information.

    You will analyze both the HTML/CSS code AND a screenshot of the website to extract:
    1. Color scheme(as hex codes):
      -Primary Color: The core color that represents your brand's identity and is used most prominently.
      -Secondary Color: A complementary color that adds depth and variety to your visual palette.
      -Accent Color: A bold, eye-catching color used sparingly to highlight important elements or actions.
      -Neutral Color: Subtle tones like gray, beige, or white that provide balance and support the main colors.
      -Background Color: The base color used behind content to create contrast and visual clarity.
      -Text Color: A color chosen for legibility and contrast to ensure your message is readable on all backgrounds.
    2. Gradient colors: look for linear-gradient, radial-gradient, etc. in CSS
    3. Button/CTA styling: type (Button/Link), size (Small/Medium/Large), style (Rounded/Square/Pill), border radius
    4. Typography: font family, size, weight
    5. Brand identity: mission, vision, tone of voice (if available)

    IMPORTANT: Use the screenshot to visually verify and identify the actual colors being used on the website.
    The screenshot shows the real rendered appearance, which may differ from CSS values due to overlays,
    opacity, or dynamic styling. Cross-reference the visual colors in the screenshot with the CSS
    to determine the most accurate brand colors.

    For colors, prioritize what you see in the screenshot over CSS values when there are discrepancies.
    Look for the most prominent and consistently used colors in headers, buttons, backgrounds, and text.
    For missing values, provide reasonable defaults based on common web design patterns.

    Analyze the website to extract brand identity elements like mission, vision, and tone of voice.

  analysis_prompt_template: |
    Analyze this website with URL: {url}

    HTML Sample:
    {html_sample}

    CSS Sample:
    {css_sample}

    Text content sample (for mission/vision analysis):
    {text_content}

    Extract the brand guidelines and return as JSON with these exact keys:
    {{
        "primary_color": "#hex",
        "secondary_color": "#hex",
        "accent_color": "#hex",
        "neutral_color": "#hex",
        "background_color": "#hex",
        "text_color": "#hex",
        "has_gradient": true/false,
        "gradient_colors": "description of gradient colors or null",
        "gradient_direction": "direction of gradient or null",
        "cta_type": "Button or Link",
        "cta_size": "Small/Medium/Large",
        "button_style": "Rounded/Square/Pill",
        "border_radius": "size with unit",
        "font": "Font Family",
        "font_size": "size with unit",
        "font_weight": "Normal/Bold/etc",
        "mission": "Mission statement if found",
        "vision": "Vision statement if found",
        "brand_personality": "One of the following brand archetypes: The Innocent, Everyman, Hero, Outlaw, Explorer, Creator, Ruler, Magician, Lover, Caregiver, Jester, or Sage",
        "brand_personality_reasoning": "Explanation of why this brand archetype was selected, based on website content, visuals, messaging, tone, and overall brand presentation",
        "tone_of_voice": "3-4 brand tone of voice Separated by Commas",
        "archetype_scores": {{
            "Creator": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Sage": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Caregiver": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Innocent": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Jester": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Magician": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Ruler": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Hero": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Everyman": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Rebel": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Explorer": {{"score": 0-10, "reasoning": "Detailed reasoning"}},
            "Lover": {{"score": 0-10, "reasoning": "Detailed reasoning"}}
        }}
    }}

archetype_analysis:
  system_message: |
    You are a brand archetype expert who analyzes brand content to determine alignment with the 12 brand archetypes.
    For each archetype, provide:
    1. A percentage score (0-100%) indicating how well the brand aligns with that archetype
    2. Detailed reasoning explaining why the brand does or doesn't align with that archetype

    Base your analysis on the brand's mission, vision, personality, tone of voice, and other provided information.
    Be specific and reference actual elements from the brand in your reasoning.

  analysis_prompt_template: |
    Analyze the following brand information and determine the alignment with each of the 12 brand archetypes.

    Brand URL: {organization_url}
    Mission: {mission}
    Vision: {vision}
    Brand Personality: {brand_personality}
    Tone of Voice: {tone_of_voice}

    For each of the following archetypes, provide:
    1. A percentage score (0-100%) indicating how well the brand aligns with that archetype
    2. Detailed reasoning explaining why the brand does or doesn't align with that archetype

    The 12 archetypes are:
    - Creator: Innovates to build original, lasting products or experiences that express their vision.
    - Sage: Seeks truth and wisdom to enlighten others through knowledge and insight.
    - Caregiver: Protects and nurtures others with compassion and selflessness.
    - Innocent: Spreads joy and optimism by living simply and doing what's right.
    - Jester: Brings happiness through humor, fun, and lightheartedness.
    - Magician: Transforms reality to create awe-inspiring, dream-like experiences.
    - Ruler: Leads with authority and order to achieve control, success, and stability.
    - Hero: Strives to overcome challenges and inspire through courage and determination.
    - Everyman: Relatable and grounded, values connection and belonging for all.
    - Rebel: Challenges norms to spark change and revolution with bold independence.
    - Explorer: Embarks on adventures to discover new experiences and personal freedom.
    - Lover: Pursues deep emotional and physical connections through passion and desire.

    Return your analysis as a JSON object with this structure:
    {{
        "Creator": {{"score": 75, "reasoning": "Detailed reasoning..."}},
        "Sage": {{"score": 45, "reasoning": "Detailed reasoning..."}},
        ...
    }}

    Be specific in your reasoning and reference actual elements from the brand information provided.

competitive_analysis:
  comparison_summary_prompt: |
    I have a comparison table of {our_product_name} versus its competitors. Here's the data:

    {comparison_data}

    Please provide a concise summary (maximum 300 words) of this comparison that highlights:
    1. The key strengths of {our_product_name} compared to competitors
    2. Areas where competitors might have an advantage
    3. The overall competitive position of {our_product_name} in the market
    4. Any notable patterns or insights from the comparison

    Format the summary in clear paragraphs with bullet points for key takeaways.

  system_message: |
    You are a competitive analysis expert who provides clear, concise, and insightful summaries of product comparisons.

crew_agents:
  master_agent:
    role: "Master Chat Agent"
    goal: "Coordinate product analysis and marketing campaign generation"
    backstory: |
      You are the master chat agent for OpenEngage, a marketing automation system.
      Your role is to coordinate product analysis and delegate tasks to specialized agents.

popup_fallback:
  default_template: |
    Generate personalized popup content based on user data.
    Return a JSON with popup_title, popup_text, and button_text fields.

whatsapp_template:
  default_example: |
    Hi {{1}},

    {{2}}

    Best regards,
    Analytics Vidhya

email_formatting:
  greeting_patterns:
    - "Hi"
    - "Hello"
    - "Dear"
    - "Hey"

  signature_patterns:
    - "Best regards"
    - "Regards"
    - "Sincerely"
    - "Best"
    - "Cheers"
    - "Thanks"
    - "Thank you"

fallback_content:
  email_emergency_fallback:
    subject: "Personalized recommendation for {first_name}"
    content: |
      Hi {first_name},

      We have a personalized recommendation for you based on your recent activity.

      Check out {product_name} at {product_url}

      Best regards,
      {sender_name}
    preheader: "A personalized recommendation just for you"

  email_static_fallback:
    subject: "Personalized recommendation for {first_name}"
    preheader: "A special recommendation just for you"
    content: |
      Hi {first_name},

      Based on your recent activity, we thought you might be interested in our latest offerings.

      Best regards,
      The Team

  popup_fallback:
    title: "Discover {priority_1_product}"
    text: "Enhance your AI skills today!"
    button: "Learn More"

  cta_fallback_options:
    - "Learn More"
    - "Get Started"
    - "Join Now"
    - "Explore Now"

default_settings:
  communication:
    sender_name: "{org_name}"
    style: "friendly"
    length: "100-150 words"
    utm_source: "email"
    utm_medium: "Email"
    utm_campaign: "product_launch"
    utm_content: "initial"

  brand_guidelines:
    primary_color: "#default"
    secondary_color: "#default"
    accent_color: "#default"
    neutral_color: "#default"
    background_color: "#default"
    text_color: "#default"
    cta_type: "Button"
    cta_size: "Medium"
    button_style: "Rounded"
    border_radius: "4px"
    font: "Default system font"
    font_size: "16px"
    font_weight: "Normal"

  feature_toggles:
    product_setup: true
    template_generator: true
    template_verification: true
    journey_builder: true
    trigger_points: true
    mass_campaign: true
    analytics_dashboard: true

session_management:
  welcome_task:
    description: "Prepare a friendly welcome message for a new user. Today's date is {current_date}."
    expected_output: "A friendly welcome message"
    context: "You are helping users with OpenEngage. Today's date is {current_date}. Provide a friendly welcome message without mentioning URLs unless specifically asked."

  default_email_settings:
    tone: "professional"
    style: "formal"
    length: "100-150 words"
    sender_name: "OpenEngage Team"