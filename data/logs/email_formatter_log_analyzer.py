#!/usr/bin/env python3
"""
Email Formatter Log Analyzer

This script provides utilities to analyze and monitor logs from the email_formatter.py module.
It can parse log files, extract statistics, and provide insights into email formatting operations.
"""

import os
import re
import json
import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
import glob

class EmailFormatterLogAnalyzer:
    """Analyzer for email formatter logs."""
    
    def __init__(self, logs_directory: str = "data/logs"):
        """
        Initialize the log analyzer.
        
        Args:
            logs_directory: Directory containing log files
        """
        self.logs_directory = logs_directory
        self.log_entries = []
        self.statistics = {}
        
    def find_email_formatter_logs(self) -> List[str]:
        """Find all email formatter log files."""
        pattern = os.path.join(self.logs_directory, "email_formatter_*.log")
        log_files = glob.glob(pattern)
        log_files.sort(key=os.path.getmtime, reverse=True)  # Most recent first
        return log_files
    
    def parse_log_file(self, log_file_path: str) -> List[Dict[str, Any]]:
        """
        Parse a single log file and extract structured data.
        
        Args:
            log_file_path: Path to the log file
            
        Returns:
            List of parsed log entries
        """
        entries = []
        log_pattern = re.compile(
            r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - '
            r'(?P<module>[^-]+) - '
            r'(?P<level>[^-]+) - '
            r'(?P<function>[^:]+):(?P<line>\d+) - '
            r'(?P<message>.*)'
        )
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    match = log_pattern.match(line)
                    if match:
                        entry = {
                            'file': os.path.basename(log_file_path),
                            'line_number': line_num,
                            'timestamp': datetime.datetime.strptime(
                                match.group('timestamp'), '%Y-%m-%d %H:%M:%S'
                            ),
                            'module': match.group('module').strip(),
                            'level': match.group('level').strip(),
                            'function': match.group('function').strip(),
                            'code_line': int(match.group('line')),
                            'message': match.group('message').strip()
                        }
                        entries.append(entry)
                    else:
                        # Handle multi-line log entries or malformed entries
                        if entries:
                            entries[-1]['message'] += '\n' + line
                            
        except Exception as e:
            print(f"Error parsing log file {log_file_path}: {e}")
            
        return entries
    
    def load_all_logs(self) -> None:
        """Load and parse all email formatter log files."""
        log_files = self.find_email_formatter_logs()
        print(f"Found {len(log_files)} email formatter log files")
        
        self.log_entries = []
        for log_file in log_files:
            print(f"Parsing {log_file}...")
            entries = self.parse_log_file(log_file)
            self.log_entries.extend(entries)
            print(f"  - Loaded {len(entries)} entries")
        
        print(f"Total log entries loaded: {len(self.log_entries)}")
        
    def generate_statistics(self) -> Dict[str, Any]:
        """Generate comprehensive statistics from the log entries."""
        if not self.log_entries:
            return {}
        
        stats = {
            'total_entries': len(self.log_entries),
            'date_range': {
                'start': min(entry['timestamp'] for entry in self.log_entries),
                'end': max(entry['timestamp'] for entry in self.log_entries)
            },
            'log_levels': Counter(entry['level'] for entry in self.log_entries),
            'functions': Counter(entry['function'] for entry in self.log_entries),
            'modules': Counter(entry['module'] for entry in self.log_entries),
            'errors': [],
            'warnings': [],
            'cta_operations': [],
            'template_operations': [],
            'email_conversions': []
        }
        
        # Extract specific operation types
        for entry in self.log_entries:
            message = entry['message'].lower()
            
            # Collect errors and warnings
            if entry['level'] == 'ERROR':
                stats['errors'].append({
                    'timestamp': entry['timestamp'],
                    'function': entry['function'],
                    'message': entry['message']
                })
            elif entry['level'] == 'WARNING':
                stats['warnings'].append({
                    'timestamp': entry['timestamp'],
                    'function': entry['function'],
                    'message': entry['message']
                })
            
            # Track CTA operations
            if 'cta' in message:
                stats['cta_operations'].append({
                    'timestamp': entry['timestamp'],
                    'function': entry['function'],
                    'message': entry['message']
                })
            
            # Track template operations
            if 'template' in message:
                stats['template_operations'].append({
                    'timestamp': entry['timestamp'],
                    'function': entry['function'],
                    'message': entry['message']
                })
            
            # Track email conversions
            if 'text_to_html' in entry['function'] and entry['level'] == 'INFO':
                stats['email_conversions'].append({
                    'timestamp': entry['timestamp'],
                    'message': entry['message']
                })
        
        self.statistics = stats
        return stats
    
    def save_statistics_report(self, output_file: Optional[str] = None) -> str:
        """
        Save a comprehensive statistics report.
        
        Args:
            output_file: Optional output file path
            
        Returns:
            Path to the saved report
        """
        if not self.statistics:
            self.generate_statistics()
        
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.logs_directory, f"email_formatter_analysis_{timestamp}.json")
        
        # Convert datetime objects to strings for JSON serialization
        stats_copy = json.loads(json.dumps(self.statistics, default=str))
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats_copy, f, indent=2, ensure_ascii=False)
        
        return output_file
    
    def print_summary(self) -> None:
        """Print a summary of the log analysis."""
        if not self.statistics:
            self.generate_statistics()
        
        stats = self.statistics
        
        print("\n" + "="*60)
        print("EMAIL FORMATTER LOG ANALYSIS SUMMARY")
        print("="*60)
        
        print(f"Total Log Entries: {stats['total_entries']}")
        print(f"Date Range: {stats['date_range']['start']} to {stats['date_range']['end']}")
        
        print(f"\nLog Levels:")
        for level, count in stats['log_levels'].most_common():
            print(f"  {level}: {count}")
        
        print(f"\nTop Functions:")
        for func, count in stats['functions'].most_common(10):
            print(f"  {func}: {count}")
        
        print(f"\nEmail Conversions: {len(stats['email_conversions'])}")
        print(f"CTA Operations: {len(stats['cta_operations'])}")
        print(f"Template Operations: {len(stats['template_operations'])}")
        print(f"Errors: {len(stats['errors'])}")
        print(f"Warnings: {len(stats['warnings'])}")
        
        if stats['errors']:
            print(f"\nRecent Errors:")
            for error in stats['errors'][-5:]:  # Show last 5 errors
                print(f"  {error['timestamp']} - {error['function']}: {error['message'][:100]}...")
        
        print("="*60)


def main():
    """Main function to run the log analyzer."""
    analyzer = EmailFormatterLogAnalyzer()
    
    print("Email Formatter Log Analyzer")
    print("Loading and analyzing logs...")
    
    analyzer.load_all_logs()
    analyzer.generate_statistics()
    
    # Print summary
    analyzer.print_summary()
    
    # Save detailed report
    report_file = analyzer.save_statistics_report()
    print(f"\nDetailed analysis saved to: {report_file}")


if __name__ == "__main__":
    main()
