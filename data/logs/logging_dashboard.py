#!/usr/bin/env python3
"""
Email Formatter Logging Dashboard

A simple dashboard that provides an overview of the email formatter logging system status.
"""

import os
import json
import datetime
import glob
from typing import Dict, List, Any

class LoggingDashboard:
    """Dashboard for email formatter logging system."""
    
    def __init__(self, logs_directory: str = "data/logs"):
        """Initialize the dashboard."""
        self.logs_directory = logs_directory
        
    def get_log_files_info(self) -> Dict[str, Any]:
        """Get information about log files."""
        log_files = glob.glob(os.path.join(self.logs_directory, "email_formatter_*.log"))
        analysis_files = glob.glob(os.path.join(self.logs_directory, "email_formatter_analysis_*.json"))
        monitor_files = glob.glob(os.path.join(self.logs_directory, "monitor_report_*.json"))
        
        info = {
            'log_files': {
                'count': len(log_files),
                'files': []
            },
            'analysis_files': {
                'count': len(analysis_files),
                'files': []
            },
            'monitor_files': {
                'count': len(monitor_files),
                'files': []
            }
        }
        
        # Get details for each type
        for log_file in sorted(log_files, key=os.path.getmtime, reverse=True):
            stat = os.stat(log_file)
            info['log_files']['files'].append({
                'name': os.path.basename(log_file),
                'size': stat.st_size,
                'modified': datetime.datetime.fromtimestamp(stat.st_mtime),
                'path': log_file
            })
        
        for analysis_file in sorted(analysis_files, key=os.path.getmtime, reverse=True):
            stat = os.stat(analysis_file)
            info['analysis_files']['files'].append({
                'name': os.path.basename(analysis_file),
                'size': stat.st_size,
                'modified': datetime.datetime.fromtimestamp(stat.st_mtime),
                'path': analysis_file
            })
        
        for monitor_file in sorted(monitor_files, key=os.path.getmtime, reverse=True):
            stat = os.stat(monitor_file)
            info['monitor_files']['files'].append({
                'name': os.path.basename(monitor_file),
                'size': stat.st_size,
                'modified': datetime.datetime.fromtimestamp(stat.st_mtime),
                'path': monitor_file
            })
        
        return info
    
    def get_latest_analysis(self) -> Dict[str, Any]:
        """Get the latest analysis report."""
        analysis_files = glob.glob(os.path.join(self.logs_directory, "email_formatter_analysis_*.json"))
        
        if not analysis_files:
            return {}
        
        latest_file = max(analysis_files, key=os.path.getmtime)
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            return {'error': f"Failed to load analysis: {e}"}
    
    def get_system_health(self) -> Dict[str, Any]:
        """Assess the health of the logging system."""
        log_info = self.get_log_files_info()
        latest_analysis = self.get_latest_analysis()
        
        health = {
            'status': 'healthy',
            'issues': [],
            'recommendations': []
        }
        
        # Check if we have recent log files
        if log_info['log_files']['count'] == 0:
            health['status'] = 'warning'
            health['issues'].append("No email formatter log files found")
            health['recommendations'].append("Run test_email_formatter_logging.py to generate sample logs")
        else:
            latest_log = log_info['log_files']['files'][0]
            time_since_last_log = datetime.datetime.now() - latest_log['modified']
            
            if time_since_last_log.days > 7:
                health['status'] = 'warning'
                health['issues'].append(f"Latest log file is {time_since_last_log.days} days old")
                health['recommendations'].append("Check if email formatter is being used actively")
        
        # Check for errors in latest analysis
        if latest_analysis and 'errors' in latest_analysis:
            error_count = len(latest_analysis['errors'])
            if error_count > 0:
                if health['status'] == 'healthy':
                    health['status'] = 'warning'
                health['issues'].append(f"Found {error_count} errors in latest analysis")
                health['recommendations'].append("Review error logs and fix underlying issues")
        
        # Check disk space (basic check)
        total_size = sum(f['size'] for f in log_info['log_files']['files'])
        if total_size > 100 * 1024 * 1024:  # 100MB
            health['recommendations'].append("Consider implementing log rotation for large log files")
        
        return health
    
    def format_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def print_dashboard(self) -> None:
        """Print the dashboard to console."""
        print("=" * 70)
        print("EMAIL FORMATTER LOGGING DASHBOARD")
        print("=" * 70)
        print(f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # System Health
        health = self.get_system_health()
        status_emoji = {
            'healthy': '🟢',
            'warning': '🟡',
            'error': '🔴'
        }
        
        print(f"\n{status_emoji.get(health['status'], '⚪')} SYSTEM HEALTH: {health['status'].upper()}")
        
        if health['issues']:
            print("\n⚠️  Issues:")
            for issue in health['issues']:
                print(f"   • {issue}")
        
        if health['recommendations']:
            print("\n💡 Recommendations:")
            for rec in health['recommendations']:
                print(f"   • {rec}")
        
        # File Information
        log_info = self.get_log_files_info()
        
        print(f"\n📁 LOG FILES ({log_info['log_files']['count']} files)")
        if log_info['log_files']['files']:
            for i, file_info in enumerate(log_info['log_files']['files'][:5]):  # Show latest 5
                age = datetime.datetime.now() - file_info['modified']
                print(f"   {i+1}. {file_info['name']}")
                print(f"      Size: {self.format_size(file_info['size'])}, Age: {age.days}d {age.seconds//3600}h")
        else:
            print("   No log files found")
        
        print(f"\n📊 ANALYSIS REPORTS ({log_info['analysis_files']['count']} files)")
        if log_info['analysis_files']['files']:
            latest_analysis = log_info['analysis_files']['files'][0]
            age = datetime.datetime.now() - latest_analysis['modified']
            print(f"   Latest: {latest_analysis['name']}")
            print(f"   Age: {age.days}d {age.seconds//3600}h, Size: {self.format_size(latest_analysis['size'])}")
        else:
            print("   No analysis reports found")
        
        # Latest Analysis Summary
        latest_analysis = self.get_latest_analysis()
        if latest_analysis and 'total_entries' in latest_analysis:
            print(f"\n📈 LATEST ANALYSIS SUMMARY")
            print(f"   Total Entries: {latest_analysis['total_entries']}")
            
            if 'log_levels' in latest_analysis:
                levels = latest_analysis['log_levels']
                print(f"   Errors: {levels.get('ERROR', 0)}")
                print(f"   Warnings: {levels.get('WARNING', 0)}")
                print(f"   Info: {levels.get('INFO', 0)}")
                print(f"   Debug: {levels.get('DEBUG', 0)}")
            
            if 'cta_operations' in latest_analysis:
                print(f"   CTA Operations: {len(latest_analysis['cta_operations'])}")
            
            if 'email_conversions' in latest_analysis:
                print(f"   Email Conversions: {len(latest_analysis['email_conversions'])}")
        
        # Quick Actions
        print(f"\n🔧 QUICK ACTIONS")
        print("   • Run analysis: python data/logs/email_formatter_log_analyzer.py")
        print("   • Start monitoring: python data/logs/email_formatter_monitor.py")
        print("   • Generate test logs: python test_email_formatter_logging.py")
        print("   • View this dashboard: python data/logs/logging_dashboard.py")
        
        print("=" * 70)
    
    def save_dashboard_report(self, output_file: str = None) -> str:
        """Save dashboard data to a JSON file."""
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.logs_directory, f"dashboard_report_{timestamp}.json")
        
        dashboard_data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'system_health': self.get_system_health(),
            'file_info': self.get_log_files_info(),
            'latest_analysis': self.get_latest_analysis()
        }
        
        # Convert datetime objects to strings for JSON serialization
        def convert_datetime(obj):
            if isinstance(obj, datetime.datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dashboard_data, f, indent=2, default=convert_datetime, ensure_ascii=False)
        
        return output_file


def main():
    """Main function."""
    dashboard = LoggingDashboard()
    
    # Print dashboard
    dashboard.print_dashboard()
    
    # Ask if user wants to save report
    try:
        save_report = input("\nSave dashboard report to file? (y/N): ").strip().lower()
        if save_report in ['y', 'yes']:
            report_file = dashboard.save_dashboard_report()
            print(f"Dashboard report saved to: {report_file}")
    except KeyboardInterrupt:
        print("\nExiting...")


if __name__ == "__main__":
    main()
