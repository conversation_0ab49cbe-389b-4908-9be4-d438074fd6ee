2025-07-25 10:03:38,554 - tree_emails_generator - INFO - log_session_state_info:52 - === SESSION STATE INFORMATION ===
2025-07-25 10:03:38,555 - tree_emails_generator - INFO - log_session_state_info:56 - Journey stages: ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']
2025-07-25 10:03:38,556 - tree_emails_generator - INFO - log_session_state_info:64 - user_email not found in session state
2025-07-25 10:03:38,556 - tree_emails_generator - INFO - log_session_state_info:68 - Crew available: True
2025-07-25 10:03:38,556 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_name: Cynthia
2025-07-25 10:03:38,556 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_behavior: <PERSON>, a new visitor from Sweden, has recently shown interest in video content, particularly engaging with 'Essential Tools for GenAI: A Beginner's Guide' and 'The Importance of Community in Your AI Learning Journey.' Despite having low engagement levels, she explored these videos, indicating a cautious approach to her AI learning journey. It seems she is still in the early stages of familiarizing herself with available resources, as evidenced by her limited interaction with the platform so far.
2025-07-25 10:03:38,556 - tree_emails_generator - INFO - log_session_state_info:79 - selected_product: not found in session state
2025-07-25 10:03:38,556 - tree_emails_generator - INFO - log_session_state_info:77 - communication_settings: {'sender_name': 'Analytics Vidhya', 'style': 'friendly', 'length': '100-150 words', 'utm_source': 'email', 'utm_medium': 'Email', 'utm_campaign': 'product_launch', 'utm_content': 'initial', 'organization_url': 'https://www.analyticsvidhya.com/', 'brand_personality': 'Sage', 'tone_of_voice': 'Professional, Educational, Engaging, Community-focused'}
2025-07-25 10:03:38,557 - tree_emails_generator - INFO - log_session_state_info:81 - === END SESSION STATE INFORMATION ===
2025-07-25 10:03:38,557 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_1 ===
2025-07-25 10:03:38,557 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=New Visitor, level=0, position=0, max_level=2, generation_mode=template
2025-07-25 10:03:38,557 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:03:38,557 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:03:38,557 - tree_emails_generator - INFO - generate_tree_emails:782 - Processing ROOT NODE node_1 for stage: New Visitor
2025-07-25 10:03:38,557 - tree_emails_generator - DEBUG - generate_tree_emails:784 - Calling generate_node_email with stage=New Visitor, generation_mode=template
2025-07-25 10:03:46,141 - tree_emails_generator - INFO - generate_tree_emails:788 - Successfully generated email for node_1
2025-07-25 10:03:46,141 - tree_emails_generator - DEBUG - generate_tree_emails:789 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:03:46,141 - tree_emails_generator - DEBUG - generate_tree_emails:792 - Set node_type to 'initial' for node_1
2025-07-25 10:03:46,141 - tree_emails_generator - DEBUG - generate_tree_emails:797 - Recipient email: None
2025-07-25 10:03:46,142 - tree_emails_generator - DEBUG - generate_tree_emails:799 - Converting email to HTML for node_1
2025-07-25 10:03:46,145 - tree_emails_generator - DEBUG - generate_tree_emails:808 - HTML conversion completed for node_1
2025-07-25 10:03:46,146 - tree_emails_generator - DEBUG - generate_tree_emails:810 - Saving email for node_1
2025-07-25 10:03:46,147 - tree_emails_generator - INFO - generate_tree_emails:812 - Successfully saved email for node_1
2025-07-25 10:03:46,147 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_1: 14.29% (1/7)
2025-07-25 10:03:46,149 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_1 (level 0 < max_level 2)
2025-07-25 10:03:46,149 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: New Visitor
2025-07-25 10:03:46,149 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Page Viewed (index 1)
2025-07-25 10:03:46,149 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_2 (follow-up), right_node_3 (next stage)
2025-07-25 10:03:46,150 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_2 (follow-up)
2025-07-25 10:03:46,150 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_2 ===
2025-07-25 10:03:46,150 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=New Visitor, level=1, position=0, max_level=2, generation_mode=template
2025-07-25 10:03:46,151 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:03:46,151 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:03:46,151 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_2 - Parent: node_1, Is Right Child: False
2025-07-25 10:03:46,151 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_2 (stage: New Visitor)
2025-07-25 10:03:46,151 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_1
2025-07-25 10:03:46,151 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_1
2025-07-25 10:03:46,151 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-25 10:03:46,151 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_2 with parent context
2025-07-25 10:03:50,503 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_2
2025-07-25 10:03:50,503 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:03:50,503 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_2
2025-07-25 10:03:50,503 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_2
2025-07-25 10:03:50,507 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_2
2025-07-25 10:03:50,507 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_2
2025-07-25 10:03:50,508 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_2
2025-07-25 10:03:50,508 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_2: 28.57% (2/7)
2025-07-25 10:03:50,509 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_2 (level 1 < max_level 2)
2025-07-25 10:03:50,509 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: New Visitor
2025-07-25 10:03:50,509 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Lead Generated (index 2)
2025-07-25 10:03:50,509 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_4 (follow-up), right_node_5 (next stage)
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_4 (follow-up)
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_4 ===
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=New Visitor, level=2, position=0, max_level=2, generation_mode=template
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:03:50,510 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_4 - Parent: node_2, Is Right Child: False
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_4 (stage: New Visitor)
2025-07-25 10:03:50,510 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_2
2025-07-25 10:03:50,510 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_2
2025-07-25 10:03:50,511 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-25 10:03:50,511 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_4 with parent context
2025-07-25 10:03:54,677 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_4
2025-07-25 10:03:54,678 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:03:54,678 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_4
2025-07-25 10:03:54,678 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_4
2025-07-25 10:03:54,682 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_4
2025-07-25 10:03:54,682 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_4
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_4
2025-07-25 10:03:54,684 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_4: 57.14% (4/7)
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_4 - no children to generate
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_4 ===
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_4
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_5 (next stage: Product Lead Generated)
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_5 ===
2025-07-25 10:03:54,684 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=1, max_level=2, generation_mode=template
2025-07-25 10:03:54,685 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:03:54,685 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:03:54,685 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_5 - Parent: node_2, Is Right Child: True
2025-07-25 10:03:54,685 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_5 (stage: Product Lead Generated)
2025-07-25 10:03:54,685 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_5
2025-07-25 10:03:59,410 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_5
2025-07-25 10:03:59,411 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:03:59,411 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_5
2025-07-25 10:03:59,411 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_5
2025-07-25 10:03:59,413 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_5
2025-07-25 10:03:59,413 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_5
2025-07-25 10:03:59,415 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_5
2025-07-25 10:03:59,415 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_5: 71.43% (5/7)
2025-07-25 10:03:59,415 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_5 - no children to generate
2025-07-25 10:03:59,416 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_5 ===
2025-07-25 10:03:59,416 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_5
2025-07-25 10:03:59,416 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_2 ===
2025-07-25 10:03:59,416 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_2
2025-07-25 10:03:59,416 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_3 (next stage: Product Page Viewed)
2025-07-25 10:03:59,417 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_3 ===
2025-07-25 10:03:59,417 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Page Viewed, level=1, position=1, max_level=2, generation_mode=template
2025-07-25 10:03:59,417 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:03:59,417 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:03:59,417 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_3 - Parent: node_1, Is Right Child: True
2025-07-25 10:03:59,417 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_3 (stage: Product Page Viewed)
2025-07-25 10:03:59,417 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_3
2025-07-25 10:04:03,095 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_3
2025-07-25 10:04:03,095 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:04:03,095 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_3
2025-07-25 10:04:03,095 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_3
2025-07-25 10:04:03,098 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_3
2025-07-25 10:04:03,098 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_3
2025-07-25 10:04:03,099 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_3
2025-07-25 10:04:03,099 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_3: 42.86% (3/7)
2025-07-25 10:04:03,100 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_3 (level 1 < max_level 2)
2025-07-25 10:04:03,100 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Page Viewed
2025-07-25 10:04:03,100 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Lead Generated (index 2)
2025-07-25 10:04:03,100 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_6 (follow-up), right_node_7 (next stage)
2025-07-25 10:04:03,100 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_6 (follow-up)
2025-07-25 10:04:03,100 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_6 ===
2025-07-25 10:04:03,100 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Page Viewed, level=2, position=2, max_level=2, generation_mode=template
2025-07-25 10:04:03,100 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:04:03,101 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:04:03,101 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_6 - Parent: node_3, Is Right Child: False
2025-07-25 10:04:03,101 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_6 (stage: Product Page Viewed)
2025-07-25 10:04:03,101 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_3
2025-07-25 10:04:03,102 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_3
2025-07-25 10:04:03,102 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-25 10:04:03,102 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_6 with parent context
2025-07-25 10:04:07,083 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_6
2025-07-25 10:04:07,083 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:04:07,083 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_6
2025-07-25 10:04:07,083 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_6
2025-07-25 10:04:07,085 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_6
2025-07-25 10:04:07,085 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_6
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_6
2025-07-25 10:04:07,086 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_6: 85.71% (6/7)
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_6 - no children to generate
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_6 ===
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_6
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_7 (next stage: Product Lead Generated)
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_7 ===
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=3, max_level=2, generation_mode=template
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 10:04:07,086 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 10:04:07,086 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_7 - Parent: node_3, Is Right Child: True
2025-07-25 10:04:07,087 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_7 (stage: Product Lead Generated)
2025-07-25 10:04:07,087 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_7
2025-07-25 10:04:12,929 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_7
2025-07-25 10:04:12,930 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 10:04:12,930 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_7
2025-07-25 10:04:12,930 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_7
2025-07-25 10:04:12,933 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_7
2025-07-25 10:04:12,933 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_7
2025-07-25 10:04:12,934 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_7
2025-07-25 10:04:12,934 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_7: 100.00% (7/7)
2025-07-25 10:04:12,935 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_7 - no children to generate
2025-07-25 10:04:12,935 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_7 ===
2025-07-25 10:04:12,935 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_7
2025-07-25 10:04:12,935 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_3 ===
2025-07-25 10:04:12,936 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_3
2025-07-25 10:04:12,936 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_1 ===
2025-07-25 10:04:12,936 - tree_emails_generator - INFO - log_tree_generation_summary:89 - === TREE GENERATION SUMMARY ===
2025-07-25 10:04:12,936 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_1: SUCCESS - Type: initial, Stage: New Visitor, Subject: Unlock Your AI Journey, Cynthia! Explore Agentic A...
2025-07-25 10:04:12,937 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_2: SUCCESS - Type: follow_up, Stage: New Visitor, Subject: Your Journey in AI Awaits!...
2025-07-25 10:04:12,937 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_3: SUCCESS - Type: next_stage, Stage: Product Page Viewed, Subject: Unlock Your AI Potential, Cynthia!\n...
2025-07-25 10:04:12,937 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_4: SUCCESS - Type: follow_up, Stage: New Visitor, Subject: Continue Your Journey in AI with Confidence!...
2025-07-25 10:04:12,938 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_5: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Explore the Essentials of AI with Confidence!\n...
2025-07-25 10:04:12,938 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_6: SUCCESS - Type: follow_up, Stage: Product Page Viewed, Subject: Great to See Your Interest in AI, Cynthia!...
2025-07-25 10:04:12,938 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_7: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Discover the Path to AI Mastery, Cynthia!\n...
2025-07-25 10:04:12,938 - tree_emails_generator - INFO - log_tree_generation_summary:106 - === END TREE GENERATION SUMMARY ===
