2025-07-24 16:54:24,676 - tree_emails_generator - INFO - log_session_state_info:52 - === SESSION STATE INFORMATION ===
2025-07-24 16:54:24,677 - tree_emails_generator - INFO - log_session_state_info:56 - Journey stages: ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']
2025-07-24 16:54:24,677 - tree_emails_generator - INFO - log_session_state_info:64 - user_email not found in session state
2025-07-24 16:54:24,677 - tree_emails_generator - INFO - log_session_state_info:68 - Crew available: True
2025-07-24 16:54:24,678 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_name: Justin
2025-07-24 16:54:24,678 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_behavior: <PERSON>, a Product Lead from Canada, has recently shown a medium level of engagement with our blog content, particularly gravitating towards the article 'The Benefits of Collaborative Projects in AI Education.' Given his role, he seems to be interested in enhancing team dynamics within AI education. He has been actively reading during weekday mornings, indicating a deliberate approach to professional development. This focused reading pattern suggests he may be looking to implement collaborative strategies in his current projects.
2025-07-24 16:54:24,678 - tree_emails_generator - INFO - log_session_state_info:79 - selected_product: not found in session state
2025-07-24 16:54:24,678 - tree_emails_generator - INFO - log_session_state_info:77 - communication_settings: {'sender_name': 'Analytics Vidhya', 'style': 'friendly', 'length': '100-150 words', 'utm_source': 'email', 'utm_medium': 'Email', 'utm_campaign': 'product_launch', 'utm_content': 'initial', 'organization_url': 'https://www.analyticsvidhya.com/', 'brand_personality': 'Sage', 'tone_of_voice': 'Professional, Educational, Engaging, Community-focused'}
2025-07-24 16:54:24,678 - tree_emails_generator - INFO - log_session_state_info:81 - === END SESSION STATE INFORMATION ===
2025-07-24 16:54:24,678 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_1 ===
2025-07-24 16:54:24,679 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=0, position=0, max_level=2, generation_mode=template
2025-07-24 16:54:24,679 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:54:24,679 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:54:24,679 - tree_emails_generator - INFO - generate_tree_emails:782 - Processing ROOT NODE node_1 for stage: Product Lead Generated
2025-07-24 16:54:24,679 - tree_emails_generator - DEBUG - generate_tree_emails:784 - Calling generate_node_email with stage=Product Lead Generated, generation_mode=template
2025-07-24 16:54:52,138 - tree_emails_generator - INFO - generate_tree_emails:788 - Successfully generated email for node_1
2025-07-24 16:54:52,138 - tree_emails_generator - DEBUG - generate_tree_emails:789 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:54:52,138 - tree_emails_generator - DEBUG - generate_tree_emails:792 - Set node_type to 'initial' for node_1
2025-07-24 16:54:52,139 - tree_emails_generator - DEBUG - generate_tree_emails:797 - Recipient email: None
2025-07-24 16:54:52,139 - tree_emails_generator - DEBUG - generate_tree_emails:799 - Converting email to HTML for node_1
2025-07-24 16:54:52,143 - tree_emails_generator - DEBUG - generate_tree_emails:808 - HTML conversion completed for node_1
2025-07-24 16:54:52,143 - tree_emails_generator - DEBUG - generate_tree_emails:810 - Saving email for node_1
2025-07-24 16:54:52,145 - tree_emails_generator - INFO - generate_tree_emails:812 - Successfully saved email for node_1
2025-07-24 16:54:52,145 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_1: 14.29% (1/7)
2025-07-24 16:54:52,146 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_1 (level 0 < max_level 2)
2025-07-24 16:54:52,146 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-24 16:54:52,146 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Purchased (index 3)
2025-07-24 16:54:52,146 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_2 (follow-up), right_node_3 (next stage)
2025-07-24 16:54:52,147 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_2 (follow-up)
2025-07-24 16:54:52,147 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_2 ===
2025-07-24 16:54:52,147 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=1, position=0, max_level=2, generation_mode=template
2025-07-24 16:54:52,147 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:54:52,147 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:54:52,147 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_2 - Parent: node_1, Is Right Child: False
2025-07-24 16:54:52,147 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_2 (stage: Product Lead Generated)
2025-07-24 16:54:52,147 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_1
2025-07-24 16:54:52,148 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_1
2025-07-24 16:54:52,148 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 16:54:52,148 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_2 with parent context
2025-07-24 16:54:57,194 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_2
2025-07-24 16:54:57,194 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:54:57,194 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_2
2025-07-24 16:54:57,195 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_2
2025-07-24 16:54:57,197 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_2
2025-07-24 16:54:57,198 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_2
2025-07-24 16:54:57,199 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_2
2025-07-24 16:54:57,199 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_2: 28.57% (2/7)
2025-07-24 16:54:57,199 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_2 (level 1 < max_level 2)
2025-07-24 16:54:57,200 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-24 16:54:57,200 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Lead Generated (index 2)
2025-07-24 16:54:57,200 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-24 16:54:57,200 - tree_emails_generator - INFO - generate_tree_emails:934 - Right child stage index 4 exceeds journey_stages length 4, using last stage: Product Purchased (index 3)
2025-07-24 16:54:57,200 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_4 (follow-up), right_node_5 (next stage)
2025-07-24 16:54:57,200 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_4 (follow-up)
2025-07-24 16:54:57,201 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_4 ===
2025-07-24 16:54:57,201 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=0, max_level=2, generation_mode=template
2025-07-24 16:54:57,201 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:54:57,201 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:54:57,201 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_4 - Parent: node_2, Is Right Child: False
2025-07-24 16:54:57,201 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_4 (stage: Product Lead Generated)
2025-07-24 16:54:57,201 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_2
2025-07-24 16:54:57,201 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_2
2025-07-24 16:54:57,201 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 16:54:57,201 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_4 with parent context
2025-07-24 16:55:30,202 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_4
2025-07-24 16:55:30,203 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:55:30,203 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_4
2025-07-24 16:55:30,203 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_4
2025-07-24 16:55:30,208 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_4
2025-07-24 16:55:30,208 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_4
2025-07-24 16:55:30,209 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_4
2025-07-24 16:55:30,210 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_4: 57.14% (4/7)
2025-07-24 16:55:30,211 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_4 - no children to generate
2025-07-24 16:55:30,211 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_4 ===
2025-07-24 16:55:30,212 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_4
2025-07-24 16:55:30,212 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_5 (next stage: Product Purchased)
2025-07-24 16:55:30,212 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_5 ===
2025-07-24 16:55:30,213 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=1, max_level=2, generation_mode=template
2025-07-24 16:55:30,213 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:55:30,213 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:55:30,213 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_5 - Parent: node_2, Is Right Child: True
2025-07-24 16:55:30,213 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_5 (stage: Product Purchased)
2025-07-24 16:55:30,213 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_5
2025-07-24 16:55:35,117 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_5
2025-07-24 16:55:35,118 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:55:35,118 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_5
2025-07-24 16:55:35,118 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_5
2025-07-24 16:55:35,120 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_5
2025-07-24 16:55:35,120 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_5
2025-07-24 16:55:35,122 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_5
2025-07-24 16:55:35,122 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_5: 71.43% (5/7)
2025-07-24 16:55:35,122 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_5 - no children to generate
2025-07-24 16:55:35,122 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_5 ===
2025-07-24 16:55:35,122 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_5
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_2 ===
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_2
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_3 (next stage: Product Purchased)
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_3 ===
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=1, position=1, max_level=2, generation_mode=template
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:55:35,123 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_3 - Parent: node_1, Is Right Child: True
2025-07-24 16:55:35,123 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_3 (stage: Product Purchased)
2025-07-24 16:55:35,123 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_3
2025-07-24 16:55:39,174 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_3
2025-07-24 16:55:39,175 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:55:39,175 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_3
2025-07-24 16:55:39,175 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_3
2025-07-24 16:55:39,177 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_3
2025-07-24 16:55:39,177 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_3
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_3
2025-07-24 16:55:39,179 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_3: 42.86% (3/7)
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_3 (level 1 < max_level 2)
2025-07-24 16:55:39,179 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Purchased
2025-07-24 16:55:39,179 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Purchased (index 3)
2025-07-24 16:55:39,179 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:937 - Current stage 'Product Purchased' is already at the last stage (index 3), skipping right child generation
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_6 (follow-up), right_node_7 (next stage)
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_6 (follow-up)
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_6 ===
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=2, max_level=2, generation_mode=template
2025-07-24 16:55:39,179 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:55:39,180 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:55:39,180 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_6 - Parent: node_3, Is Right Child: False
2025-07-24 16:55:39,180 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_6 (stage: Product Purchased)
2025-07-24 16:55:39,180 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_3
2025-07-24 16:55:39,181 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_3
2025-07-24 16:55:39,181 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 16:55:39,181 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_6 with parent context
2025-07-24 16:55:44,217 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_6
2025-07-24 16:55:44,217 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:55:44,217 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_6
2025-07-24 16:55:44,217 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_6
2025-07-24 16:55:44,219 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_6
2025-07-24 16:55:44,219 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_6
2025-07-24 16:55:44,221 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_6
2025-07-24 16:55:44,221 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_6: 85.71% (6/7)
2025-07-24 16:55:44,221 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_6 - no children to generate
2025-07-24 16:55:44,221 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_6 ===
2025-07-24 16:55:44,221 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_6
2025-07-24 16:55:44,221 - tree_emails_generator - WARNING - generate_tree_emails:957 - Skipping right child generation - no stage available for node_7
2025-07-24 16:55:44,221 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_3 ===
2025-07-24 16:55:44,222 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_3
2025-07-24 16:55:44,222 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_1 ===
2025-07-24 16:55:44,222 - tree_emails_generator - INFO - log_tree_generation_summary:89 - === TREE GENERATION SUMMARY ===
2025-07-24 16:55:44,223 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_1: SUCCESS - Type: initial, Stage: Product Lead Generated, Subject: Elevate Your AI Collaboration Skills, Justin!...
2025-07-24 16:55:44,223 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_2: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Unlock Your Potential in AI Education, Justin!...
2025-07-24 16:55:44,224 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_3: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Your Next Step in AI Mastery Awaits, Justin!...
2025-07-24 16:55:44,224 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_4: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Your Next Step in AI Mastery...
2025-07-24 16:55:44,225 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_5: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Unlock Your Potential with the Agentic AI Pioneer ...
2025-07-24 16:55:44,225 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_6: SUCCESS - Type: follow_up, Stage: Product Purchased, Subject: Ready to Explore More in AI Education?...
2025-07-24 16:55:44,225 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_7: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Discover the Future of AI Learning, Amanda!...
2025-07-24 16:55:44,225 - tree_emails_generator - INFO - log_tree_generation_summary:106 - === END TREE GENERATION SUMMARY ===
