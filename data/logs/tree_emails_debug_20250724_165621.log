2025-07-24 16:56:40,397 - tree_emails_generator - INFO - log_session_state_info:52 - === SESSION STATE INFORMATION ===
2025-07-24 16:56:40,398 - tree_emails_generator - INFO - log_session_state_info:56 - Journey stages: ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']
2025-07-24 16:56:40,398 - tree_emails_generator - INFO - log_session_state_info:64 - user_email not found in session state
2025-07-24 16:56:40,398 - tree_emails_generator - INFO - log_session_state_info:68 - Crew available: True
2025-07-24 16:56:40,398 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_name: Justin
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_behavior: <PERSON>, a Product Lead from Canada, has recently shown a medium level of engagement with our blog content, particularly gravitating towards the article 'The Benefits of Collaborative Projects in AI Education.' Given his role, he seems to be interested in enhancing team dynamics within AI education. He has been actively reading during weekday mornings, indicating a deliberate approach to professional development. This focused reading pattern suggests he may be looking to implement collaborative strategies in his current projects.
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - log_session_state_info:79 - selected_product: not found in session state
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - log_session_state_info:77 - communication_settings: {'sender_name': 'Analytics Vidhya', 'style': 'friendly', 'length': '100-150 words', 'utm_source': 'email', 'utm_medium': 'Email', 'utm_campaign': 'product_launch', 'utm_content': 'initial', 'organization_url': 'https://www.analyticsvidhya.com/', 'brand_personality': 'Sage', 'tone_of_voice': 'Professional, Educational, Engaging, Community-focused'}
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - log_session_state_info:81 - === END SESSION STATE INFORMATION ===
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_1 ===
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=0, position=0, max_level=2, generation_mode=template
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:56:40,399 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:56:40,399 - tree_emails_generator - INFO - generate_tree_emails:782 - Processing ROOT NODE node_1 for stage: Product Lead Generated
2025-07-24 16:56:40,399 - tree_emails_generator - DEBUG - generate_tree_emails:784 - Calling generate_node_email with stage=Product Lead Generated, generation_mode=template
2025-07-24 16:56:46,072 - tree_emails_generator - INFO - generate_tree_emails:788 - Successfully generated email for node_1
2025-07-24 16:56:46,072 - tree_emails_generator - DEBUG - generate_tree_emails:789 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:56:46,072 - tree_emails_generator - DEBUG - generate_tree_emails:792 - Set node_type to 'initial' for node_1
2025-07-24 16:56:46,073 - tree_emails_generator - DEBUG - generate_tree_emails:797 - Recipient email: None
2025-07-24 16:56:46,073 - tree_emails_generator - DEBUG - generate_tree_emails:799 - Converting email to HTML for node_1
2025-07-24 16:56:46,077 - tree_emails_generator - DEBUG - generate_tree_emails:808 - HTML conversion completed for node_1
2025-07-24 16:56:46,077 - tree_emails_generator - DEBUG - generate_tree_emails:810 - Saving email for node_1
2025-07-24 16:56:46,078 - tree_emails_generator - INFO - generate_tree_emails:812 - Successfully saved email for node_1
2025-07-24 16:56:46,078 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_1: 14.29% (1/7)
2025-07-24 16:56:46,078 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_1 (level 0 < max_level 2)
2025-07-24 16:56:46,079 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-24 16:56:46,079 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Purchased (index 3)
2025-07-24 16:56:46,079 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_2 (follow-up), right_node_3 (next stage)
2025-07-24 16:56:46,079 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_2 (follow-up)
2025-07-24 16:56:46,079 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_2 ===
2025-07-24 16:56:46,079 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=1, position=0, max_level=2, generation_mode=template
2025-07-24 16:56:46,079 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:56:46,079 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:56:46,079 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_2 - Parent: node_1, Is Right Child: False
2025-07-24 16:56:46,080 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_2 (stage: Product Lead Generated)
2025-07-24 16:56:46,080 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_1
2025-07-24 16:56:46,080 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_1
2025-07-24 16:56:46,080 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 16:56:46,080 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_2 with parent context
2025-07-24 16:56:50,281 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_2
2025-07-24 16:56:50,282 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:56:50,282 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_2
2025-07-24 16:56:50,282 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_2
2025-07-24 16:56:50,284 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_2
2025-07-24 16:56:50,284 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_2
2025-07-24 16:56:50,285 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_2
2025-07-24 16:56:50,285 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_2: 28.57% (2/7)
2025-07-24 16:56:50,286 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_2 (level 1 < max_level 2)
2025-07-24 16:56:50,286 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-24 16:56:50,287 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Lead Generated (index 2)
2025-07-24 16:56:50,287 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-24 16:56:50,287 - tree_emails_generator - INFO - generate_tree_emails:934 - Right child stage index 4 exceeds journey_stages length 4, using last stage: Product Purchased (index 3)
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_4 (follow-up), right_node_5 (next stage)
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_4 (follow-up)
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_4 ===
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=0, max_level=2, generation_mode=template
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:56:50,288 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_4 - Parent: node_2, Is Right Child: False
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_4 (stage: Product Lead Generated)
2025-07-24 16:56:50,288 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_2
2025-07-24 16:56:50,288 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_2
2025-07-24 16:56:50,288 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 16:56:50,289 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_4 with parent context
2025-07-24 16:56:57,679 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_4
2025-07-24 16:56:57,679 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:56:57,679 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_4
2025-07-24 16:56:57,679 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_4
2025-07-24 16:56:57,682 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_4
2025-07-24 16:56:57,682 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_4
2025-07-24 16:56:57,684 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_4
2025-07-24 16:56:57,684 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_4: 57.14% (4/7)
2025-07-24 16:56:57,684 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_4 - no children to generate
2025-07-24 16:56:57,684 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_4 ===
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_4
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_5 (next stage: Product Purchased)
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_5 ===
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=1, max_level=2, generation_mode=template
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:56:57,685 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_5 - Parent: node_2, Is Right Child: True
2025-07-24 16:56:57,685 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_5 (stage: Product Purchased)
2025-07-24 16:56:57,686 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_5
2025-07-24 16:57:07,257 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_5
2025-07-24 16:57:07,259 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:57:07,259 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_5
2025-07-24 16:57:07,259 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_5
2025-07-24 16:57:07,261 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_5
2025-07-24 16:57:07,261 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_5
2025-07-24 16:57:07,262 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_5
2025-07-24 16:57:07,262 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_5: 71.43% (5/7)
2025-07-24 16:57:07,265 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_5 - no children to generate
2025-07-24 16:57:07,266 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_5 ===
2025-07-24 16:57:07,266 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_5
2025-07-24 16:57:07,266 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_2 ===
2025-07-24 16:57:07,268 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_2
2025-07-24 16:57:07,269 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_3 (next stage: Product Purchased)
2025-07-24 16:57:07,269 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_3 ===
2025-07-24 16:57:07,269 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=1, position=1, max_level=2, generation_mode=template
2025-07-24 16:57:07,269 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:57:07,269 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:57:07,269 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_3 - Parent: node_1, Is Right Child: True
2025-07-24 16:57:07,269 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_3 (stage: Product Purchased)
2025-07-24 16:57:07,269 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_3
2025-07-24 16:57:11,260 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_3
2025-07-24 16:57:11,260 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:57:11,260 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_3
2025-07-24 16:57:11,260 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_3
2025-07-24 16:57:11,262 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_3
2025-07-24 16:57:11,263 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_3
2025-07-24 16:57:11,264 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_3
2025-07-24 16:57:11,264 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_3: 42.86% (3/7)
2025-07-24 16:57:11,264 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_3 (level 1 < max_level 2)
2025-07-24 16:57:11,264 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Purchased
2025-07-24 16:57:11,264 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Purchased (index 3)
2025-07-24 16:57:11,264 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:937 - Current stage 'Product Purchased' is already at the last stage (index 3), skipping right child generation
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_6 (follow-up), right_node_7 (next stage)
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_6 (follow-up)
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_6 ===
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=2, max_level=2, generation_mode=template
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 16:57:11,265 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_6 - Parent: node_3, Is Right Child: False
2025-07-24 16:57:11,265 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_6 (stage: Product Purchased)
2025-07-24 16:57:11,266 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_3
2025-07-24 16:57:11,266 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_3
2025-07-24 16:57:11,266 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 16:57:11,266 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_6 with parent context
2025-07-24 16:57:15,761 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_6
2025-07-24 16:57:15,761 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 16:57:15,761 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_6
2025-07-24 16:57:15,761 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_6
2025-07-24 16:57:15,762 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_6
2025-07-24 16:57:15,762 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_6
2025-07-24 16:57:15,763 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_6
2025-07-24 16:57:15,763 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_6: 85.71% (6/7)
2025-07-24 16:57:15,765 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_6 - no children to generate
2025-07-24 16:57:15,766 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_6 ===
2025-07-24 16:57:15,766 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_6
2025-07-24 16:57:15,766 - tree_emails_generator - WARNING - generate_tree_emails:957 - Skipping right child generation - no stage available for node_7
2025-07-24 16:57:15,766 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_3 ===
2025-07-24 16:57:15,766 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_3
2025-07-24 16:57:15,766 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_1 ===
2025-07-24 16:57:15,767 - tree_emails_generator - INFO - log_tree_generation_summary:89 - === TREE GENERATION SUMMARY ===
2025-07-24 16:57:15,769 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_1: SUCCESS - Type: initial, Stage: Product Lead Generated, Subject: Elevate Your Team with AI Collaboration!...
2025-07-24 16:57:15,770 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_2: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Unlock Your Potential with Agentic AI...
2025-07-24 16:57:15,770 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_3: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Thank You for Joining the Agentic AI Pioneer Progr...
2025-07-24 16:57:15,771 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_4: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Explore the Next Steps in Your AI Journey...
2025-07-24 16:57:15,771 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_5: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Elevate Your AI Expertise with Us, Justin!...
2025-07-24 16:57:15,772 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_6: SUCCESS - Type: follow_up, Stage: Product Purchased, Subject: Exciting Next Steps in Your AI Journey!...
2025-07-24 16:57:15,772 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_7: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Discover the Future of AI Learning, Amanda!...
2025-07-24 16:57:15,772 - tree_emails_generator - INFO - log_tree_generation_summary:106 - === END TREE GENERATION SUMMARY ===
2025-07-24 17:05:46,899 - tree_emails_generator - INFO - log_session_state_info:52 - === SESSION STATE INFORMATION ===
2025-07-24 17:05:46,902 - tree_emails_generator - INFO - log_session_state_info:56 - Journey stages: ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']
2025-07-24 17:05:46,902 - tree_emails_generator - INFO - log_session_state_info:64 - user_email not found in session state
2025-07-24 17:05:46,902 - tree_emails_generator - INFO - log_session_state_info:68 - Crew available: True
2025-07-24 17:05:46,902 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_name: Justin
2025-07-24 17:05:46,902 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_behavior: Justin, a Product Lead from Canada, has recently shown a medium level of engagement with our blog content, particularly gravitating towards the article 'The Benefits of Collaborative Projects in AI Education.' Given his role, he seems to be interested in enhancing team dynamics within AI education. He has been actively reading during weekday mornings, indicating a deliberate approach to professional development. This focused reading pattern suggests he may be looking to implement collaborative strategies in his current projects.
2025-07-24 17:05:46,902 - tree_emails_generator - INFO - log_session_state_info:79 - selected_product: not found in session state
2025-07-24 17:05:46,903 - tree_emails_generator - INFO - log_session_state_info:77 - communication_settings: {'sender_name': 'Analytics Vidhya', 'style': 'friendly', 'length': '100-150 words', 'utm_source': 'email', 'utm_medium': 'Email', 'utm_campaign': 'product_launch', 'utm_content': 'initial', 'organization_url': 'https://www.analyticsvidhya.com/', 'brand_personality': 'Sage', 'tone_of_voice': 'Professional, Educational, Engaging, Community-focused'}
2025-07-24 17:05:46,903 - tree_emails_generator - INFO - log_session_state_info:81 - === END SESSION STATE INFORMATION ===
2025-07-24 17:05:46,903 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_1 ===
2025-07-24 17:05:46,903 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=0, position=0, max_level=2, generation_mode=template
2025-07-24 17:05:46,903 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:05:46,903 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:05:46,903 - tree_emails_generator - INFO - generate_tree_emails:782 - Processing ROOT NODE node_1 for stage: Product Lead Generated
2025-07-24 17:05:46,903 - tree_emails_generator - DEBUG - generate_tree_emails:784 - Calling generate_node_email with stage=Product Lead Generated, generation_mode=template
2025-07-24 17:05:53,578 - tree_emails_generator - INFO - generate_tree_emails:788 - Successfully generated email for node_1
2025-07-24 17:05:53,579 - tree_emails_generator - DEBUG - generate_tree_emails:789 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:05:53,579 - tree_emails_generator - DEBUG - generate_tree_emails:792 - Set node_type to 'initial' for node_1
2025-07-24 17:05:53,579 - tree_emails_generator - DEBUG - generate_tree_emails:797 - Recipient email: None
2025-07-24 17:05:53,579 - tree_emails_generator - DEBUG - generate_tree_emails:799 - Converting email to HTML for node_1
2025-07-24 17:05:53,582 - tree_emails_generator - DEBUG - generate_tree_emails:808 - HTML conversion completed for node_1
2025-07-24 17:05:53,582 - tree_emails_generator - DEBUG - generate_tree_emails:810 - Saving email for node_1
2025-07-24 17:05:53,583 - tree_emails_generator - INFO - generate_tree_emails:812 - Successfully saved email for node_1
2025-07-24 17:05:53,583 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_1: 14.29% (1/7)
2025-07-24 17:05:53,584 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_1 (level 0 < max_level 2)
2025-07-24 17:05:53,584 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-24 17:05:53,584 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Purchased (index 3)
2025-07-24 17:05:53,584 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_2 (follow-up), right_node_3 (next stage)
2025-07-24 17:05:53,584 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_2 (follow-up)
2025-07-24 17:05:53,585 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_2 ===
2025-07-24 17:05:53,585 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=1, position=0, max_level=2, generation_mode=template
2025-07-24 17:05:53,585 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:05:53,585 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:05:53,585 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_2 - Parent: node_1, Is Right Child: False
2025-07-24 17:05:53,585 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_2 (stage: Product Lead Generated)
2025-07-24 17:05:53,585 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_1
2025-07-24 17:05:53,585 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_1
2025-07-24 17:05:53,586 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 17:05:53,586 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_2 with parent context
2025-07-24 17:05:57,949 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_2
2025-07-24 17:05:57,949 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:05:57,949 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_2
2025-07-24 17:05:57,949 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_2
2025-07-24 17:05:57,951 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_2
2025-07-24 17:05:57,951 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_2
2025-07-24 17:05:57,954 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_2
2025-07-24 17:05:57,954 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_2: 28.57% (2/7)
2025-07-24 17:05:57,955 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_2 (level 1 < max_level 2)
2025-07-24 17:05:57,955 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-24 17:05:57,955 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Lead Generated (index 2)
2025-07-24 17:05:57,955 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-24 17:05:57,955 - tree_emails_generator - INFO - generate_tree_emails:934 - Right child stage index 4 exceeds journey_stages length 4, using last stage: Product Purchased (index 3)
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_4 (follow-up), right_node_5 (next stage)
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_4 (follow-up)
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_4 ===
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=0, max_level=2, generation_mode=template
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:05:57,956 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_4 - Parent: node_2, Is Right Child: False
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_4 (stage: Product Lead Generated)
2025-07-24 17:05:57,956 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_2
2025-07-24 17:05:57,956 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_2
2025-07-24 17:05:57,956 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 17:05:57,956 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_4 with parent context
2025-07-24 17:06:03,122 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_4
2025-07-24 17:06:03,124 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:06:03,124 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_4
2025-07-24 17:06:03,124 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_4
2025-07-24 17:06:03,126 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_4
2025-07-24 17:06:03,126 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_4
2025-07-24 17:06:03,127 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_4
2025-07-24 17:06:03,127 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_4: 57.14% (4/7)
2025-07-24 17:06:03,128 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_4 - no children to generate
2025-07-24 17:06:03,128 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_4 ===
2025-07-24 17:06:03,128 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_4
2025-07-24 17:06:03,128 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_5 (next stage: Product Purchased)
2025-07-24 17:06:03,128 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_5 ===
2025-07-24 17:06:03,129 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=1, max_level=2, generation_mode=template
2025-07-24 17:06:03,129 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:06:03,129 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:06:03,129 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_5 - Parent: node_2, Is Right Child: True
2025-07-24 17:06:03,129 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_5 (stage: Product Purchased)
2025-07-24 17:06:03,129 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_5
2025-07-24 17:06:07,892 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_5
2025-07-24 17:06:07,893 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:06:07,893 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_5
2025-07-24 17:06:07,893 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_5
2025-07-24 17:06:07,895 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_5
2025-07-24 17:06:07,895 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_5
2025-07-24 17:06:07,896 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_5
2025-07-24 17:06:07,897 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_5: 71.43% (5/7)
2025-07-24 17:06:07,897 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_5 - no children to generate
2025-07-24 17:06:07,897 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_5 ===
2025-07-24 17:06:07,897 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_5
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_2 ===
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_2
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_3 (next stage: Product Purchased)
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_3 ===
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=1, position=1, max_level=2, generation_mode=template
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:06:07,898 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_3 - Parent: node_1, Is Right Child: True
2025-07-24 17:06:07,898 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_3 (stage: Product Purchased)
2025-07-24 17:06:07,898 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_3
2025-07-24 17:06:10,758 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_3
2025-07-24 17:06:10,759 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:06:10,759 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_3
2025-07-24 17:06:10,759 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_3
2025-07-24 17:06:10,761 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_3
2025-07-24 17:06:10,761 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_3
2025-07-24 17:06:10,762 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_3
2025-07-24 17:06:10,762 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_3: 42.86% (3/7)
2025-07-24 17:06:10,762 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_3 (level 1 < max_level 2)
2025-07-24 17:06:10,762 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Purchased
2025-07-24 17:06:10,763 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Purchased (index 3)
2025-07-24 17:06:10,763 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-24 17:06:10,763 - tree_emails_generator - INFO - generate_tree_emails:937 - Current stage 'Product Purchased' is already at the last stage (index 3), skipping right child generation
2025-07-24 17:06:10,763 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_6 (follow-up), right_node_7 (next stage)
2025-07-24 17:06:10,763 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_6 (follow-up)
2025-07-24 17:06:10,763 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_6 ===
2025-07-24 17:06:10,763 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=2, max_level=2, generation_mode=template
2025-07-24 17:06:10,763 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:06:10,764 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:06:10,764 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_6 - Parent: node_3, Is Right Child: False
2025-07-24 17:06:10,764 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_6 (stage: Product Purchased)
2025-07-24 17:06:10,764 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_3
2025-07-24 17:06:10,764 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_3
2025-07-24 17:06:10,764 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 17:06:10,764 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_6 with parent context
2025-07-24 17:06:14,958 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_6
2025-07-24 17:06:14,958 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:06:14,959 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_6
2025-07-24 17:06:14,959 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_6
2025-07-24 17:06:14,960 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_6
2025-07-24 17:06:14,960 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_6
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_6
2025-07-24 17:06:14,961 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_6: 85.71% (6/7)
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_6 - no children to generate
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_6 ===
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_6
2025-07-24 17:06:14,961 - tree_emails_generator - WARNING - generate_tree_emails:957 - Skipping right child generation - no stage available for node_7
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_3 ===
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_3
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_1 ===
2025-07-24 17:06:14,961 - tree_emails_generator - INFO - log_tree_generation_summary:89 - === TREE GENERATION SUMMARY ===
2025-07-24 17:06:14,962 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_1: SUCCESS - Type: initial, Stage: Product Lead Generated, Subject: Elevate Your AI Expertise with Collaborative Insig...
2025-07-24 17:06:14,962 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_2: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Unlock Your Potential with AI Learning...
2025-07-24 17:06:14,962 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_3: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Elevate Your Team’s AI Skills, Justin!...
2025-07-24 17:06:14,962 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_4: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Your Next Step in AI Education Awaits!...
2025-07-24 17:06:14,962 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_5: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Thank You for Joining the Agentic AI Pioneer Progr...
2025-07-24 17:06:14,962 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_6: SUCCESS - Type: follow_up, Stage: Product Purchased, Subject: Exciting Next Steps in Your AI Journey!...
2025-07-24 17:06:14,963 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_7: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Discover the Future of AI Learning, Amanda!...
2025-07-24 17:06:14,963 - tree_emails_generator - INFO - log_tree_generation_summary:106 - === END TREE GENERATION SUMMARY ===
2025-07-24 17:09:47,790 - tree_emails_generator - INFO - log_session_state_info:52 - === SESSION STATE INFORMATION ===
2025-07-24 17:09:47,792 - tree_emails_generator - INFO - log_session_state_info:56 - Journey stages: ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']
2025-07-24 17:09:47,792 - tree_emails_generator - INFO - log_session_state_info:64 - user_email not found in session state
2025-07-24 17:09:47,792 - tree_emails_generator - INFO - log_session_state_info:68 - Crew available: True
2025-07-24 17:09:47,792 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_name: Julia
2025-07-24 17:09:47,792 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_behavior: Julia, a new visitor from Brazil, is currently exploring the world of AI with a keen interest in video content. Recently, she has watched informative videos such as 'AI Education Myths Debunked: What You Really Need to Know' and 'The Role of Networking in Building an AI Career'. With a medium engagement level, Julia appears to be actively seeking resources to enhance her knowledge, as evidenced by her interest in 'How to Stay Updated on AI Developments: Resources and Tips'.
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - log_session_state_info:79 - selected_product: not found in session state
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - log_session_state_info:77 - communication_settings: {'sender_name': 'Analytics Vidhya', 'style': 'friendly', 'length': '100-150 words', 'utm_source': 'email', 'utm_medium': 'Email', 'utm_campaign': 'product_launch', 'utm_content': 'initial', 'organization_url': 'https://www.analyticsvidhya.com/', 'brand_personality': 'Sage', 'tone_of_voice': 'Professional, Educational, Engaging, Community-focused'}
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - log_session_state_info:81 - === END SESSION STATE INFORMATION ===
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_1 ===
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=New Visitor, level=0, position=0, max_level=2, generation_mode=template
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:09:47,793 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:09:47,793 - tree_emails_generator - INFO - generate_tree_emails:782 - Processing ROOT NODE node_1 for stage: New Visitor
2025-07-24 17:09:47,793 - tree_emails_generator - DEBUG - generate_tree_emails:784 - Calling generate_node_email with stage=New Visitor, generation_mode=template
2025-07-24 17:09:54,794 - tree_emails_generator - INFO - generate_tree_emails:788 - Successfully generated email for node_1
2025-07-24 17:09:54,795 - tree_emails_generator - DEBUG - generate_tree_emails:789 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:09:54,795 - tree_emails_generator - DEBUG - generate_tree_emails:792 - Set node_type to 'initial' for node_1
2025-07-24 17:09:54,795 - tree_emails_generator - DEBUG - generate_tree_emails:797 - Recipient email: None
2025-07-24 17:09:54,795 - tree_emails_generator - DEBUG - generate_tree_emails:799 - Converting email to HTML for node_1
2025-07-24 17:09:54,798 - tree_emails_generator - DEBUG - generate_tree_emails:808 - HTML conversion completed for node_1
2025-07-24 17:09:54,798 - tree_emails_generator - DEBUG - generate_tree_emails:810 - Saving email for node_1
2025-07-24 17:09:54,799 - tree_emails_generator - INFO - generate_tree_emails:812 - Successfully saved email for node_1
2025-07-24 17:09:54,799 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_1: 14.29% (1/7)
2025-07-24 17:09:54,800 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_1 (level 0 < max_level 2)
2025-07-24 17:09:54,800 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: New Visitor
2025-07-24 17:09:54,801 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Page Viewed (index 1)
2025-07-24 17:09:54,801 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_2 (follow-up), right_node_3 (next stage)
2025-07-24 17:09:54,801 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_2 (follow-up)
2025-07-24 17:09:54,801 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_2 ===
2025-07-24 17:09:54,801 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=New Visitor, level=1, position=0, max_level=2, generation_mode=template
2025-07-24 17:09:54,801 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:09:54,801 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:09:54,801 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_2 - Parent: node_1, Is Right Child: False
2025-07-24 17:09:54,802 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_2 (stage: New Visitor)
2025-07-24 17:09:54,802 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_1
2025-07-24 17:09:54,802 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_1
2025-07-24 17:09:54,802 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 17:09:54,802 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_2 with parent context
2025-07-24 17:10:00,659 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_2
2025-07-24 17:10:00,660 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:10:00,660 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_2
2025-07-24 17:10:00,660 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_2
2025-07-24 17:10:00,662 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_2
2025-07-24 17:10:00,662 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_2
2025-07-24 17:10:00,664 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_2
2025-07-24 17:10:00,664 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_2: 28.57% (2/7)
2025-07-24 17:10:00,665 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_2 (level 1 < max_level 2)
2025-07-24 17:10:00,665 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: New Visitor
2025-07-24 17:10:00,665 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Lead Generated (index 2)
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_4 (follow-up), right_node_5 (next stage)
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_4 (follow-up)
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_4 ===
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=New Visitor, level=2, position=0, max_level=2, generation_mode=template
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:10:00,666 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_4 - Parent: node_2, Is Right Child: False
2025-07-24 17:10:00,666 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_4 (stage: New Visitor)
2025-07-24 17:10:00,666 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_2
2025-07-24 17:10:00,667 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_2
2025-07-24 17:10:00,667 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 17:10:00,667 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_4 with parent context
2025-07-24 17:10:05,775 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_4
2025-07-24 17:10:05,776 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:10:05,776 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_4
2025-07-24 17:10:05,776 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_4
2025-07-24 17:10:05,778 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_4
2025-07-24 17:10:05,779 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_4
2025-07-24 17:10:05,780 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_4
2025-07-24 17:10:05,780 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_4: 57.14% (4/7)
2025-07-24 17:10:05,782 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_4 - no children to generate
2025-07-24 17:10:05,782 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_4 ===
2025-07-24 17:10:05,782 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_4
2025-07-24 17:10:05,783 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_5 (next stage: Product Lead Generated)
2025-07-24 17:10:05,783 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_5 ===
2025-07-24 17:10:05,783 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=1, max_level=2, generation_mode=template
2025-07-24 17:10:05,783 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:10:05,783 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:10:05,783 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_5 - Parent: node_2, Is Right Child: True
2025-07-24 17:10:05,783 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_5 (stage: Product Lead Generated)
2025-07-24 17:10:05,783 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_5
2025-07-24 17:10:10,488 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_5
2025-07-24 17:10:10,488 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:10:10,488 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_5
2025-07-24 17:10:10,488 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_5
2025-07-24 17:10:10,490 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_5
2025-07-24 17:10:10,490 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_5
2025-07-24 17:10:10,493 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_5
2025-07-24 17:10:10,493 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_5: 71.43% (5/7)
2025-07-24 17:10:10,493 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_5 - no children to generate
2025-07-24 17:10:10,493 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_5 ===
2025-07-24 17:10:10,493 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_5
2025-07-24 17:10:10,493 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_2 ===
2025-07-24 17:10:10,494 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_2
2025-07-24 17:10:10,494 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_3 (next stage: Product Page Viewed)
2025-07-24 17:10:10,494 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_3 ===
2025-07-24 17:10:10,494 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Page Viewed, level=1, position=1, max_level=2, generation_mode=template
2025-07-24 17:10:10,495 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:10:10,495 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:10:10,495 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_3 - Parent: node_1, Is Right Child: True
2025-07-24 17:10:10,495 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_3 (stage: Product Page Viewed)
2025-07-24 17:10:10,495 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_3
2025-07-24 17:10:15,369 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_3
2025-07-24 17:10:15,370 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:10:15,370 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_3
2025-07-24 17:10:15,370 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_3
2025-07-24 17:10:15,372 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_3
2025-07-24 17:10:15,372 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_3
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_3
2025-07-24 17:10:15,373 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_3: 42.86% (3/7)
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_3 (level 1 < max_level 2)
2025-07-24 17:10:15,373 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Page Viewed
2025-07-24 17:10:15,373 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Lead Generated (index 2)
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_6 (follow-up), right_node_7 (next stage)
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_6 (follow-up)
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_6 ===
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Page Viewed, level=2, position=2, max_level=2, generation_mode=template
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:10:15,373 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_6 - Parent: node_3, Is Right Child: False
2025-07-24 17:10:15,373 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_6 (stage: Product Page Viewed)
2025-07-24 17:10:15,374 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_3
2025-07-24 17:10:15,374 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_3
2025-07-24 17:10:15,374 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-24 17:10:15,374 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_6 with parent context
2025-07-24 17:10:20,018 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_6
2025-07-24 17:10:20,018 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:10:20,018 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_6
2025-07-24 17:10:20,018 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_6
2025-07-24 17:10:20,020 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_6
2025-07-24 17:10:20,020 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_6
2025-07-24 17:10:20,022 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_6
2025-07-24 17:10:20,022 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_6: 85.71% (6/7)
2025-07-24 17:10:20,023 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_6 - no children to generate
2025-07-24 17:10:20,023 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_6 ===
2025-07-24 17:10:20,024 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_6
2025-07-24 17:10:20,024 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_7 (next stage: Product Lead Generated)
2025-07-24 17:10:20,025 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_7 ===
2025-07-24 17:10:20,025 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=3, max_level=2, generation_mode=template
2025-07-24 17:10:20,025 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-24 17:10:20,025 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-24 17:10:20,025 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_7 - Parent: node_3, Is Right Child: True
2025-07-24 17:10:20,025 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_7 (stage: Product Lead Generated)
2025-07-24 17:10:20,025 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_7
2025-07-24 17:10:24,508 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_7
2025-07-24 17:10:24,509 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-24 17:10:24,509 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_7
2025-07-24 17:10:24,509 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_7
2025-07-24 17:10:24,511 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_7
2025-07-24 17:10:24,511 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_7
2025-07-24 17:10:24,512 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_7
2025-07-24 17:10:24,512 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_7: 100.00% (7/7)
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_7 - no children to generate
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_7 ===
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_7
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_3 ===
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_3
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_1 ===
2025-07-24 17:10:24,513 - tree_emails_generator - INFO - log_tree_generation_summary:89 - === TREE GENERATION SUMMARY ===
2025-07-24 17:10:24,514 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_1: SUCCESS - Type: initial, Stage: New Visitor, Subject: Unlock Your AI Potential, Julia!...
2025-07-24 17:10:24,515 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_2: SUCCESS - Type: follow_up, Stage: New Visitor, Subject: Ready to Dive Deeper into AI, Julia?...
2025-07-24 17:10:24,515 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_3: SUCCESS - Type: next_stage, Stage: Product Page Viewed, Subject: Take Your AI Knowledge to the Next Level, Julia!...
2025-07-24 17:10:24,515 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_4: SUCCESS - Type: follow_up, Stage: New Visitor, Subject: Let's Dive Deeper into AI, Julia!...
2025-07-24 17:10:24,516 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_5: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Unlock Your AI Potential Today, Julia!...
2025-07-24 17:10:24,516 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_6: SUCCESS - Type: follow_up, Stage: Product Page Viewed, Subject: Your Journey into AI Awaits!...
2025-07-24 17:10:24,516 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_7: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Unlock Your AI Potential Today, Julia!...
2025-07-24 17:10:24,516 - tree_emails_generator - INFO - log_tree_generation_summary:106 - === END TREE GENERATION SUMMARY ===
