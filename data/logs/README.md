# Email Formatter Logging System

This directory contains comprehensive logging functionality for the OpenEngage email formatter module. The logging system captures detailed information about email formatting operations, CTA generation, template processing, and error handling.

## Overview

The email formatter logging system provides:

- **Detailed Operation Tracking**: Logs all major operations including email conversions, CTA generation, and template processing
- **Error and Warning Monitoring**: Captures and categorizes all errors and warnings for debugging
- **Performance Analysis**: Tracks function call patterns and processing statistics
- **Real-time Monitoring**: Live monitoring capabilities for production environments
- **Comprehensive Reporting**: Automated analysis and reporting of log data

## Log Files

### Email Formatter Logs
- **Pattern**: `email_formatter_YYYYMMDD_HHMMSS.log`
- **Content**: Detailed logs from the email formatter module
- **Format**: Structured logging with timestamp, module, level, function, line number, and message

### Analysis Reports
- **Pattern**: `email_formatter_analysis_YYYYMMDD_HHMMSS.json`
- **Content**: Comprehensive statistical analysis of log data
- **Generated by**: `email_formatter_log_analyzer.py`

### Monitor Reports
- **Pattern**: `monitor_report_YYYYMMDD_HHMMSS.json`
- **Content**: Real-time monitoring statistics and recent entries
- **Generated by**: `email_formatter_monitor.py`

## Tools and Scripts

### 1. Email Formatter Log Analyzer (`email_formatter_log_analyzer.py`)

A comprehensive analysis tool that processes email formatter logs and generates detailed statistics.

**Features:**
- Parses all email formatter log files
- Generates comprehensive statistics
- Identifies error patterns and trends
- Tracks CTA and template operations
- Exports detailed JSON reports

**Usage:**
```bash
python data/logs/email_formatter_log_analyzer.py
```

**Output:**
- Console summary of log analysis
- Detailed JSON report saved to logs directory

### 2. Email Formatter Monitor (`email_formatter_monitor.py`)

A real-time monitoring tool for live log analysis and alerting.

**Features:**
- Real-time log file monitoring
- Live error and warning alerts
- Interactive command interface
- Statistics tracking
- Report generation

**Usage:**
```bash
python data/logs/email_formatter_monitor.py
```

**Commands:**
- `start` - Start monitoring logs
- `stop` - Stop monitoring logs
- `status` - Show current status
- `report` - Save monitoring report
- `quit` - Exit the monitor

### 3. Test Script (`test_email_formatter_logging.py`)

A comprehensive test script that exercises the email formatter module to generate sample logs.

**Features:**
- Tests all major email formatter functions
- Generates various log scenarios
- Tests error handling
- Validates logging functionality

**Usage:**
```bash
python test_email_formatter_logging.py
```

## Log Levels and Categories

### Log Levels
- **DEBUG**: Detailed diagnostic information
- **INFO**: General operational information
- **WARNING**: Warning conditions that don't prevent operation
- **ERROR**: Error conditions that may affect functionality

### Key Log Categories

#### Email Conversion Operations
- Email content processing
- HTML generation
- Paragraph processing
- Content validation

#### CTA Operations
- CTA text loading from templates
- Pattern matching for CTA selection
- CTA button generation
- CTA placement in emails

#### Template Operations
- Template loading and processing
- Brand guidelines application
- Template-specific customizations

#### Error Handling
- Input validation errors
- File access issues
- Processing exceptions
- Configuration problems

## Configuration

### Logging Setup
The logging system is automatically configured when the email formatter module is imported. Key configuration:

- **Log Directory**: `data/logs/`
- **Log Level**: DEBUG (captures all log levels)
- **File Format**: Detailed with timestamp, module, function, and line number
- **Console Format**: Simplified for immediate feedback
- **Encoding**: UTF-8 for international character support

### Customization
To modify logging behavior, edit the `setup_email_formatter_logging()` function in `src/openengage/core/email_formatter.py`.

## Analysis and Insights

### Common Log Patterns

1. **Successful Email Conversion**:
   ```
   INFO - text_to_html - Starting text_to_html conversion
   DEBUG - text_to_html - Input parameters - product_url: ..., template_name: ...
   DEBUG - process_paragraphs - Split content into X paragraphs
   INFO - create_html_email_template - Creating CTA button with text: '...'
   ```

2. **CTA Template Loading**:
   ```
   DEBUG - load_template_cta - Loading CTA for template: ...
   DEBUG - load_template_cta - Loaded X CTA templates from file
   INFO - load_template_cta - Found CTA for template '...': ...
   ```

3. **Error Scenarios**:
   ```
   WARNING - text_to_html - Invalid email_content provided
   ERROR - create_html_email_template - Error in primary CTA placement approach: ...
   ```

### Performance Metrics
The analysis tools track:
- Function call frequency
- Error rates by function
- Template usage patterns
- Processing time patterns
- Resource utilization

## Troubleshooting

### Common Issues

1. **Missing Log Files**
   - Ensure the `data/logs/` directory exists
   - Check file permissions
   - Verify the email formatter module is being used

2. **Import Errors**
   - Ensure you're running scripts from the project root
   - Check Python path configuration
   - Verify all dependencies are installed

3. **Analysis Tool Issues**
   - Check log file format compatibility
   - Ensure sufficient disk space
   - Verify JSON output directory permissions

### Debug Mode
To enable more verbose logging, modify the log level in the setup function:
```python
logger.setLevel(logging.DEBUG)  # Already enabled
```

## Integration

### With Existing Systems
The logging system is designed to integrate with:
- Application monitoring tools
- Log aggregation systems
- Alert management platforms
- Performance monitoring solutions

### API Integration
Log data can be accessed programmatically:
```python
from data.logs.email_formatter_log_analyzer import EmailFormatterLogAnalyzer

analyzer = EmailFormatterLogAnalyzer()
analyzer.load_all_logs()
stats = analyzer.generate_statistics()
```

## Maintenance

### Log Rotation
Consider implementing log rotation for production environments:
- Archive old log files
- Compress historical data
- Set retention policies
- Monitor disk usage

### Regular Analysis
Schedule regular analysis runs:
- Daily summary reports
- Weekly trend analysis
- Monthly performance reviews
- Quarterly optimization assessments

## Support

For issues with the logging system:
1. Check this README for common solutions
2. Review recent log files for error patterns
3. Run the test script to validate functionality
4. Use the monitor tool for real-time debugging

---

*Last updated: July 25, 2025*
*Version: 1.0*
