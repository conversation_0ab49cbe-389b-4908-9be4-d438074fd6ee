#!/usr/bin/env python3
"""
Email Formatter Log Monitor

This script provides real-time monitoring of email formatter logs.
It can watch for new log entries and provide live analysis.
"""

import os
import time
import json
import datetime
from typing import Dict, List, Any, Optional
import threading
import queue
from collections import defaultdict, deque

class EmailFormatterMonitor:
    """Real-time monitor for email formatter logs."""
    
    def __init__(self, logs_directory: str = "data/logs", max_recent_entries: int = 100):
        """
        Initialize the monitor.
        
        Args:
            logs_directory: Directory containing log files
            max_recent_entries: Maximum number of recent entries to keep in memory
        """
        self.logs_directory = logs_directory
        self.max_recent_entries = max_recent_entries
        self.recent_entries = deque(maxlen=max_recent_entries)
        self.statistics = defaultdict(int)
        self.error_count = 0
        self.warning_count = 0
        self.is_monitoring = False
        self.monitor_thread = None
        
    def get_latest_log_file(self) -> Optional[str]:
        """Get the path to the most recent email formatter log file."""
        try:
            log_files = []
            for filename in os.listdir(self.logs_directory):
                if filename.startswith('email_formatter_') and filename.endswith('.log'):
                    filepath = os.path.join(self.logs_directory, filename)
                    log_files.append((filepath, os.path.getmtime(filepath)))
            
            if log_files:
                # Return the most recently modified file
                return max(log_files, key=lambda x: x[1])[0]
            return None
        except Exception as e:
            print(f"Error finding latest log file: {e}")
            return None
    
    def parse_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single log line into structured data."""
        import re
        
        log_pattern = re.compile(
            r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - '
            r'(?P<module>[^-]+) - '
            r'(?P<level>[^-]+) - '
            r'(?P<function>[^:]+):(?P<line>\d+) - '
            r'(?P<message>.*)'
        )
        
        match = log_pattern.match(line.strip())
        if match:
            try:
                return {
                    'timestamp': datetime.datetime.strptime(
                        match.group('timestamp'), '%Y-%m-%d %H:%M:%S'
                    ),
                    'module': match.group('module').strip(),
                    'level': match.group('level').strip(),
                    'function': match.group('function').strip(),
                    'code_line': int(match.group('line')),
                    'message': match.group('message').strip()
                }
            except Exception:
                return None
        return None
    
    def process_log_entry(self, entry: Dict[str, Any]) -> None:
        """Process a new log entry and update statistics."""
        self.recent_entries.append(entry)
        
        # Update statistics
        self.statistics[f"level_{entry['level']}"] += 1
        self.statistics[f"function_{entry['function']}"] += 1
        self.statistics['total_entries'] += 1
        
        # Track errors and warnings
        if entry['level'] == 'ERROR':
            self.error_count += 1
            print(f"🔴 ERROR in {entry['function']}: {entry['message']}")
        elif entry['level'] == 'WARNING':
            self.warning_count += 1
            print(f"🟡 WARNING in {entry['function']}: {entry['message']}")
        elif entry['level'] == 'INFO':
            # Only show important INFO messages
            message = entry['message'].lower()
            if any(keyword in message for keyword in ['starting', 'creating', 'found', 'using']):
                print(f"ℹ️  {entry['function']}: {entry['message']}")
    
    def monitor_logs(self) -> None:
        """Monitor log files for new entries."""
        current_log_file = None
        file_position = 0
        
        while self.is_monitoring:
            try:
                # Check for new log file
                latest_log_file = self.get_latest_log_file()
                
                if latest_log_file != current_log_file:
                    current_log_file = latest_log_file
                    file_position = 0
                    if current_log_file:
                        print(f"📁 Monitoring new log file: {os.path.basename(current_log_file)}")
                
                if current_log_file and os.path.exists(current_log_file):
                    # Read new lines from the log file
                    with open(current_log_file, 'r', encoding='utf-8') as f:
                        f.seek(file_position)
                        new_lines = f.readlines()
                        file_position = f.tell()
                    
                    # Process new lines
                    for line in new_lines:
                        if line.strip():
                            entry = self.parse_log_line(line)
                            if entry:
                                self.process_log_entry(entry)
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(5)  # Wait longer on error
    
    def start_monitoring(self) -> None:
        """Start monitoring logs in a separate thread."""
        if self.is_monitoring:
            print("Monitoring is already running")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_logs, daemon=True)
        self.monitor_thread.start()
        print("📊 Email formatter log monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop monitoring logs."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("🛑 Email formatter log monitoring stopped")
    
    def get_current_statistics(self) -> Dict[str, Any]:
        """Get current monitoring statistics."""
        return {
            'total_entries': self.statistics['total_entries'],
            'errors': self.error_count,
            'warnings': self.warning_count,
            'recent_entries_count': len(self.recent_entries),
            'functions': {k.replace('function_', ''): v for k, v in self.statistics.items() if k.startswith('function_')},
            'levels': {k.replace('level_', ''): v for k, v in self.statistics.items() if k.startswith('level_')},
            'monitoring_status': 'active' if self.is_monitoring else 'stopped'
        }
    
    def print_status(self) -> None:
        """Print current monitoring status."""
        stats = self.get_current_statistics()
        
        print("\n" + "="*50)
        print("EMAIL FORMATTER MONITOR STATUS")
        print("="*50)
        print(f"Status: {stats['monitoring_status'].upper()}")
        print(f"Total Entries: {stats['total_entries']}")
        print(f"Errors: {stats['errors']}")
        print(f"Warnings: {stats['warnings']}")
        print(f"Recent Entries: {stats['recent_entries_count']}")
        
        if stats['levels']:
            print("\nLog Levels:")
            for level, count in stats['levels'].items():
                print(f"  {level}: {count}")
        
        if stats['functions']:
            print("\nTop Functions:")
            sorted_functions = sorted(stats['functions'].items(), key=lambda x: x[1], reverse=True)
            for func, count in sorted_functions[:5]:
                print(f"  {func}: {count}")
        
        print("="*50)
    
    def save_monitoring_report(self, output_file: Optional[str] = None) -> str:
        """Save a monitoring report."""
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.logs_directory, f"monitor_report_{timestamp}.json")
        
        report = {
            'timestamp': datetime.datetime.now().isoformat(),
            'statistics': self.get_current_statistics(),
            'recent_entries': [
                {
                    'timestamp': entry['timestamp'].isoformat(),
                    'level': entry['level'],
                    'function': entry['function'],
                    'message': entry['message']
                }
                for entry in list(self.recent_entries)[-20:]  # Last 20 entries
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return output_file


def main():
    """Main function for interactive monitoring."""
    monitor = EmailFormatterMonitor()
    
    print("Email Formatter Log Monitor")
    print("Commands: start, stop, status, report, quit")
    
    try:
        while True:
            command = input("\nMonitor> ").strip().lower()
            
            if command == 'start':
                monitor.start_monitoring()
            elif command == 'stop':
                monitor.stop_monitoring()
            elif command == 'status':
                monitor.print_status()
            elif command == 'report':
                report_file = monitor.save_monitoring_report()
                print(f"Report saved to: {report_file}")
            elif command in ['quit', 'exit', 'q']:
                break
            elif command == 'help':
                print("Available commands:")
                print("  start  - Start monitoring logs")
                print("  stop   - Stop monitoring logs")
                print("  status - Show current status")
                print("  report - Save monitoring report")
                print("  quit   - Exit the monitor")
            else:
                print("Unknown command. Type 'help' for available commands.")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        monitor.stop_monitoring()


if __name__ == "__main__":
    main()
