2025-07-25 12:01:16,557 - tree_emails_generator - INFO - log_session_state_info:52 - === SESSION STATE INFORMATION ===
2025-07-25 12:01:16,558 - tree_emails_generator - INFO - log_session_state_info:56 - Journey stages: ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']
2025-07-25 12:01:16,558 - tree_emails_generator - INFO - log_session_state_info:64 - user_email not found in session state
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - log_session_state_info:68 - Crew available: True
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_name: <PERSON><PERSON>
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - log_session_state_info:77 - journey_user_behavior: <PERSON><PERSON>, a Product Lead based in Japan, has been engaging with blog content focused on artificial intelligence education and career advancement. Recently, she viewed articles like 'A Deep Dive into the Latest Trends in Artificial Intelligence Education' and 'Understanding the Curriculum: What to Expect from AI Programs,' but her engagement level remains low, suggesting she may be in the initial stages of her research. Despite opening the last email sent to her, there has been no significant interaction with product pages, indicating a cautious approach to her decision-making process.
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - log_session_state_info:79 - selected_product: not found in session state
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - log_session_state_info:77 - communication_settings: {'sender_name': 'Analytics Vidhya', 'style': 'friendly', 'length': '100-150 words', 'utm_source': 'email', 'utm_medium': 'Email', 'utm_campaign': 'product_launch', 'utm_content': 'initial', 'organization_url': 'https://www.analyticsvidhya.com/', 'brand_personality': 'Sage', 'tone_of_voice': 'Professional, Educational, Engaging, Community-focused'}
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - log_session_state_info:81 - === END SESSION STATE INFORMATION ===
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_1 ===
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=0, position=0, max_level=2, generation_mode=template
2025-07-25 12:01:16,559 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 12:01:16,559 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 12:01:16,560 - tree_emails_generator - INFO - generate_tree_emails:782 - Processing ROOT NODE node_1 for stage: Product Lead Generated
2025-07-25 12:01:16,560 - tree_emails_generator - DEBUG - generate_tree_emails:784 - Calling generate_node_email with stage=Product Lead Generated, generation_mode=template
2025-07-25 12:01:22,911 - tree_emails_generator - INFO - generate_tree_emails:788 - Successfully generated email for node_1
2025-07-25 12:01:22,912 - tree_emails_generator - DEBUG - generate_tree_emails:789 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 12:01:22,912 - tree_emails_generator - DEBUG - generate_tree_emails:792 - Set node_type to 'initial' for node_1
2025-07-25 12:01:22,913 - tree_emails_generator - DEBUG - generate_tree_emails:797 - Recipient email: None
2025-07-25 12:01:22,913 - tree_emails_generator - DEBUG - generate_tree_emails:799 - Converting email to HTML for node_1
2025-07-25 12:01:22,918 - tree_emails_generator - DEBUG - generate_tree_emails:808 - HTML conversion completed for node_1
2025-07-25 12:01:22,918 - tree_emails_generator - DEBUG - generate_tree_emails:810 - Saving email for node_1
2025-07-25 12:01:22,920 - tree_emails_generator - INFO - generate_tree_emails:812 - Successfully saved email for node_1
2025-07-25 12:01:22,920 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_1: 14.29% (1/7)
2025-07-25 12:01:22,921 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_1 (level 0 < max_level 2)
2025-07-25 12:01:22,921 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-25 12:01:22,921 - tree_emails_generator - DEBUG - generate_tree_emails:915 - Right child stage: Product Purchased (index 3)
2025-07-25 12:01:22,921 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_2 (follow-up), right_node_3 (next stage)
2025-07-25 12:01:22,921 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_2 (follow-up)
2025-07-25 12:01:22,922 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_2 ===
2025-07-25 12:01:22,923 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=1, position=0, max_level=2, generation_mode=template
2025-07-25 12:01:22,923 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 12:01:22,923 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 12:01:22,923 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_2 - Parent: node_1, Is Right Child: False
2025-07-25 12:01:22,923 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_2 (stage: Product Lead Generated)
2025-07-25 12:01:22,923 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_1
2025-07-25 12:01:22,924 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_1
2025-07-25 12:01:22,924 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-25 12:01:22,924 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_2 with parent context
2025-07-25 12:01:26,262 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_2
2025-07-25 12:01:26,262 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 12:01:26,262 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_2
2025-07-25 12:01:26,262 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_2
2025-07-25 12:01:26,266 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_2
2025-07-25 12:01:26,266 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_2
2025-07-25 12:01:26,268 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_2
2025-07-25 12:01:26,269 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_2: 28.57% (2/7)
2025-07-25 12:01:26,269 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_2 (level 1 < max_level 2)
2025-07-25 12:01:26,269 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Lead Generated
2025-07-25 12:01:26,269 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Lead Generated (index 2)
2025-07-25 12:01:26,269 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-25 12:01:26,269 - tree_emails_generator - INFO - generate_tree_emails:934 - Right child stage index 4 exceeds journey_stages length 4, using last stage: Product Purchased (index 3)
2025-07-25 12:01:26,269 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_4 (follow-up), right_node_5 (next stage)
2025-07-25 12:01:26,270 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_4 (follow-up)
2025-07-25 12:01:26,270 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_4 ===
2025-07-25 12:01:26,270 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Lead Generated, level=2, position=0, max_level=2, generation_mode=template
2025-07-25 12:01:26,270 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 12:01:26,270 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 12:01:26,270 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_4 - Parent: node_2, Is Right Child: False
2025-07-25 12:01:26,270 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_4 (stage: Product Lead Generated)
2025-07-25 12:01:26,270 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_2
2025-07-25 12:01:26,271 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_2
2025-07-25 12:01:26,271 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-25 12:01:26,271 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_4 with parent context
2025-07-25 12:01:30,301 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_4
2025-07-25 12:01:30,302 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 12:01:30,303 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_4
2025-07-25 12:01:30,303 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_4
2025-07-25 12:01:30,305 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_4
2025-07-25 12:01:30,305 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_4
2025-07-25 12:01:30,307 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_4
2025-07-25 12:01:30,307 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_4: 57.14% (4/7)
2025-07-25 12:01:30,307 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_4 - no children to generate
2025-07-25 12:01:30,307 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_4 ===
2025-07-25 12:01:30,307 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_4
2025-07-25 12:01:30,308 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_5 (next stage: Product Purchased)
2025-07-25 12:01:30,308 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_5 ===
2025-07-25 12:01:30,308 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=1, max_level=2, generation_mode=template
2025-07-25 12:01:30,308 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 12:01:30,308 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 12:01:30,308 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_5 - Parent: node_2, Is Right Child: True
2025-07-25 12:01:30,308 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_5 (stage: Product Purchased)
2025-07-25 12:01:30,308 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_5
2025-07-25 12:01:35,657 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_5
2025-07-25 12:01:35,657 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 12:01:35,657 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_5
2025-07-25 12:01:35,657 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_5
2025-07-25 12:01:35,660 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_5
2025-07-25 12:01:35,660 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_5
2025-07-25 12:01:35,661 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_5
2025-07-25 12:01:35,661 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_5: 71.43% (5/7)
2025-07-25 12:01:35,662 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_5 - no children to generate
2025-07-25 12:01:35,662 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_5 ===
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_5
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_2 ===
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_2
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:953 - Starting recursive call for RIGHT CHILD node_3 (next stage: Product Purchased)
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_3 ===
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=1, position=1, max_level=2, generation_mode=template
2025-07-25 12:01:35,663 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 12:01:35,663 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 12:01:35,664 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_3 - Parent: node_1, Is Right Child: True
2025-07-25 12:01:35,664 - tree_emails_generator - INFO - generate_tree_emails:826 - Generating NEXT STAGE email for node_3 (stage: Product Purchased)
2025-07-25 12:01:35,664 - tree_emails_generator - DEBUG - generate_tree_emails:828 - Calling generate_node_email for next stage node node_3
2025-07-25 12:01:40,997 - tree_emails_generator - INFO - generate_tree_emails:832 - Successfully generated next stage email for node_3
2025-07-25 12:01:40,998 - tree_emails_generator - DEBUG - generate_tree_emails:833 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 12:01:40,998 - tree_emails_generator - DEBUG - generate_tree_emails:836 - Set node_type to 'next_stage' for node_3
2025-07-25 12:01:40,998 - tree_emails_generator - DEBUG - generate_tree_emails:839 - Converting next stage email to HTML for node_3
2025-07-25 12:01:41,002 - tree_emails_generator - DEBUG - generate_tree_emails:848 - HTML conversion completed for next stage node_3
2025-07-25 12:01:41,002 - tree_emails_generator - DEBUG - generate_tree_emails:850 - Saving next stage email for node_3
2025-07-25 12:01:41,003 - tree_emails_generator - INFO - generate_tree_emails:852 - Successfully saved next stage email for node_3
2025-07-25 12:01:41,003 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_3: 42.86% (3/7)
2025-07-25 12:01:41,003 - tree_emails_generator - INFO - generate_tree_emails:904 - Generating children for node_3 (level 1 < max_level 2)
2025-07-25 12:01:41,004 - tree_emails_generator - DEBUG - generate_tree_emails:907 - Left child stage: Product Purchased
2025-07-25 12:01:41,004 - tree_emails_generator - DEBUG - generate_tree_emails:928 - Current stage: Product Purchased (index 3)
2025-07-25 12:01:41,004 - tree_emails_generator - DEBUG - generate_tree_emails:929 - Last stage index: 3
2025-07-25 12:01:41,004 - tree_emails_generator - INFO - generate_tree_emails:937 - Current stage 'Product Purchased' is already at the last stage (index 3), skipping right child generation
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:944 - Generating children: left_node_6 (follow-up), right_node_7 (next stage)
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:947 - Starting recursive call for LEFT CHILD node_6 (follow-up)
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:774 - === Starting email generation for node_6 ===
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:775 - Parameters: stage=Product Purchased, level=2, position=2, max_level=2, generation_mode=template
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:776 - Product: Agentic AI Pioneer Program from Analytics Vidhya
2025-07-25 12:01:41,005 - tree_emails_generator - DEBUG - generate_tree_emails:777 - Full matched_product keys: ['Product_Name', 'Company_Name', 'Type_of_Product', 'Product_Features', 'Product_Summary', 'Product_URL', 'Company_URL', 'organization_url']
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:823 - Processing CHILD NODE node_6 - Parent: node_3, Is Right Child: False
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:857 - Generating FOLLOW-UP email for node_6 (stage: Product Purchased)
2025-07-25 12:01:41,005 - tree_emails_generator - DEBUG - generate_tree_emails:859 - Loading parent email from node_3
2025-07-25 12:01:41,005 - tree_emails_generator - INFO - generate_tree_emails:863 - Successfully loaded parent email from node_3
2025-07-25 12:01:41,005 - tree_emails_generator - DEBUG - generate_tree_emails:864 - Parent email keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link', 'node_type', 'html_content']
2025-07-25 12:01:41,005 - tree_emails_generator - DEBUG - generate_tree_emails:866 - Calling generate_node_email for follow-up node node_6 with parent context
2025-07-25 12:01:44,959 - tree_emails_generator - INFO - generate_tree_emails:870 - Successfully generated follow-up email for node_6
2025-07-25 12:01:44,959 - tree_emails_generator - DEBUG - generate_tree_emails:871 - Email content keys: ['subject', 'pre_header', 'content', 'generated_at', 'stage', 'settings', 'cta', 'cta_link']
2025-07-25 12:01:44,960 - tree_emails_generator - DEBUG - generate_tree_emails:874 - Set node_type to 'follow_up' for node_6
2025-07-25 12:01:44,960 - tree_emails_generator - DEBUG - generate_tree_emails:877 - Converting follow-up email to HTML for node_6
2025-07-25 12:01:44,964 - tree_emails_generator - DEBUG - generate_tree_emails:886 - HTML conversion completed for follow-up node_6
2025-07-25 12:01:44,964 - tree_emails_generator - DEBUG - generate_tree_emails:888 - Saving follow-up email for node_6
2025-07-25 12:01:44,966 - tree_emails_generator - INFO - generate_tree_emails:890 - Successfully saved follow-up email for node_6
2025-07-25 12:01:44,966 - tree_emails_generator - DEBUG - generate_tree_emails:899 - Updating progress for node_6: 85.71% (6/7)
2025-07-25 12:01:44,966 - tree_emails_generator - INFO - generate_tree_emails:959 - Reached max level 2 for node_6 - no children to generate
2025-07-25 12:01:44,966 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_6 ===
2025-07-25 12:01:44,966 - tree_emails_generator - INFO - generate_tree_emails:949 - Completed recursive call for LEFT CHILD node_6
2025-07-25 12:01:44,966 - tree_emails_generator - WARNING - generate_tree_emails:957 - Skipping right child generation - no stage available for node_7
2025-07-25 12:01:44,966 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_3 ===
2025-07-25 12:01:44,967 - tree_emails_generator - INFO - generate_tree_emails:955 - Completed recursive call for RIGHT CHILD node_3
2025-07-25 12:01:44,967 - tree_emails_generator - INFO - generate_tree_emails:961 - === Completed email generation for node_1 ===
2025-07-25 12:01:44,967 - tree_emails_generator - INFO - log_tree_generation_summary:89 - === TREE GENERATION SUMMARY ===
2025-07-25 12:01:44,968 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_1: SUCCESS - Type: initial, Stage: Product Lead Generated, Subject: Explore the Next Step in Your AI Journey, Deanna!...
2025-07-25 12:01:44,968 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_2: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Ready to Dive Deeper into AI?...
2025-07-25 12:01:44,968 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_3: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Your AI Learning Journey Awaits, Deanna!...
2025-07-25 12:01:44,969 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_4: SUCCESS - Type: follow_up, Stage: Product Lead Generated, Subject: Your Next Step in AI Mastery Awaits!...
2025-07-25 12:01:44,969 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_5: SUCCESS - Type: next_stage, Stage: Product Purchased, Subject: Your Next Step in AI Mastery Awaits!...
2025-07-25 12:01:44,969 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_6: SUCCESS - Type: follow_up, Stage: Product Purchased, Subject: Take Your AI Skills to the Next Level, Deanna!...
2025-07-25 12:01:44,970 - tree_emails_generator - INFO - log_tree_generation_summary:100 - node_7: SUCCESS - Type: next_stage, Stage: Product Lead Generated, Subject: Elevate Your AI Skills with Us, Christopher!...
2025-07-25 12:01:44,970 - tree_emails_generator - INFO - log_tree_generation_summary:106 - === END TREE GENERATION SUMMARY ===
