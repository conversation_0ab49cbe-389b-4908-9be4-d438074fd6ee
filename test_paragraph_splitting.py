#!/usr/bin/env python3
"""
Test script to verify the paragraph splitting fix
"""

import re

def test_old_logic(content):
    """Test the old problematic logic"""
    print("=== OLD LOGIC ===")
    print(f"Original content length: {len(content)}")
    
    if "\n\n" not in content and "\n" not in content:
        content = content.replace(", ",",\n\n",1).replace(". ", ".\n\n")
    
    if "\n\n" in content:
        paragraphs = content.split('\n\n')
    else:
        paragraphs = content.split('\n')
    
    print(f"Number of paragraphs: {len(paragraphs)}")
    for i, para in enumerate(paragraphs):
        print(f"Paragraph {i+1}: {para[:100]}...")
    
    return paragraphs

def test_new_logic(content):
    """Test the new improved logic"""
    print("\n=== NEW LOGIC ===")
    print(f"Original content length: {len(content)}")

    # If content has no line breaks, intelligently create paragraph breaks
    if "\n\n" not in content and "\n" not in content:
        # First, separate the greeting from the main content
        greeting_match = re.match(r'^\s*((?:Hi|Hello|Dear|Hey)\s*[^,\n]*[,.]?\s*)', content, re.IGNORECASE)

        if greeting_match:
            greeting = greeting_match.group(1).strip()
            main_content = content[greeting_match.end():].strip()

            # Split the main content into logical paragraphs
            # Look for natural break points like topic transitions
            sentences = re.split(r'(\. )', main_content)

            # Reconstruct with paragraph breaks at logical points
            reconstructed = greeting + "\n\n"
            current_paragraph = ""
            sentence_count = 0

            for i, part in enumerate(sentences):
                current_paragraph += part

                # If this is a sentence ending (". "), increment counter
                if part == ". ":
                    sentence_count += 1

                    # Look for topic transition indicators or after 2-3 sentences
                    next_sentence = sentences[i + 1] if i + 1 < len(sentences) else ""

                    # Break paragraph if:
                    # 1. We have 2+ sentences AND
                    # 2. Next sentence starts with transition words OR we have 3+ sentences
                    should_break = (
                        sentence_count >= 2 and
                        i < len(sentences) - 2 and  # Not the last sentence
                        (
                            sentence_count >= 3 or  # After 3 sentences, always break
                            re.match(r'^\s*(With|Our|This|The|Take|Explore|Best)', next_sentence, re.IGNORECASE)  # Topic transition words
                        )
                    )

                    if should_break:
                        reconstructed += current_paragraph + "\n\n"
                        current_paragraph = ""
                        sentence_count = 0

            # Add any remaining content
            if current_paragraph.strip():
                reconstructed += current_paragraph

            content = reconstructed
        else:
            # No greeting found, use simpler logic
            sentences = re.split(r'(\. )', content)
            reconstructed = ""
            sentence_count = 0

            for i, part in enumerate(sentences):
                reconstructed += part

                if part == ". ":
                    sentence_count += 1
                    if sentence_count >= 2 and i < len(sentences) - 2:
                        reconstructed += "\n\n"
                        sentence_count = 0

            content = reconstructed

    # Split content into paragraphs
    if "\n\n" in content:
        paragraphs = content.split('\n\n')
    else:
        paragraphs = content.split('\n')

    print(f"Number of paragraphs: {len(paragraphs)}")
    for i, para in enumerate(paragraphs):
        print(f"Paragraph {i+1}: {para[:100]}...")

    return paragraphs

def main():
    # Test content from the CSV
    test_content = "Dear Kenneth, Your exploration of AI tools and the importance of continuous learning signifies a strong commitment to upskilling in the dynamic field of artificial intelligence. The Agentic AI Pioneer Program is designed to support your journey by offering a structured and comprehensive roadmap to mastering AI, tailored to your aspirations. With 20+ hands-on projects, you will have the opportunity to apply theoretical knowledge in real-world scenarios, enhancing your skills and building a robust portfolio. Our program is tailored to empower you with in-depth insights and practical expertise, making you a valuable asset in the AI landscape. Take the first step towards mastering AI. Explore more about the program here: [Agentic AI Pioneer Program](https://www.analyticsvidhya.com/agenticaipioneer?utm_source=email&utm_medium=drishyam&utm_campaign=newsletter&utm_term=october2023). Best Regards, Team Analytics Vidhya"
    
    print("Testing paragraph splitting with Kenneth's email content...")
    print(f"Content preview: {test_content[:150]}...")
    print()
    
    # Test old logic
    old_paragraphs = test_old_logic(test_content)
    
    # Test new logic
    new_paragraphs = test_new_logic(test_content)
    
    print(f"\n=== COMPARISON ===")
    print(f"Old logic: {len(old_paragraphs)} paragraphs")
    print(f"New logic: {len(new_paragraphs)} paragraphs")
    
    if len(new_paragraphs) > len(old_paragraphs):
        print("✅ NEW LOGIC CREATES MORE PARAGRAPHS (Better formatting)")
    elif len(new_paragraphs) == len(old_paragraphs):
        print("⚠️  Same number of paragraphs")
    else:
        print("❌ New logic creates fewer paragraphs")

if __name__ == "__main__":
    main()
